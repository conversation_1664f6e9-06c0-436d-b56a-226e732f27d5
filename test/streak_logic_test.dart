import 'package:flutter_test/flutter_test.dart';
import 'package:unstack/logic/streak/streak_impl.dart';
import 'package:unstack/models/tasks/task.model.dart';
import 'package:unstack/providers/task_provider.dart';
import 'package:unstack/providers/streak_provider.dart';

void main() {
  group('Streak Logic Tests', () {
    late StreakManager streakManager;
    late TaskProvider taskProvider;
    late StreakProvider streakProvider;

    setUp(() {
      streakManager = StreakManager();
      taskProvider = TaskProvider();
      streakProvider = StreakProvider();
    });

    group('Streak Earning Rules', () {
      test('Day earns streak when all tasks for that day are completed', () async {
        // Create tasks for today
        final today = DateTime.now();
        final task1 = Task(
          id: '1',
          title: 'Task 1',
          description: 'Test task 1',
          priority: TaskPriority.medium,
          createdAt: today,
          isCompleted: true,
          completedAt: today,
          priorityIndex: 0,
        );
        final task2 = Task(
          id: '2',
          title: 'Task 2',
          description: 'Test task 2',
          priority: TaskPriority.medium,
          createdAt: today,
          isCompleted: true,
          completedAt: today,
          priorityIndex: 0,
        );

        // Add tasks to provider
        await taskProvider.addTask(task1);
        await taskProvider.addTask(task2);

        // Mark both as completed
        await taskProvider.markTaskAsCompleted(task1);
        await taskProvider.markTaskAsCompleted(task2);

        // Check if all tasks for today are completed
        final allCompleted = taskProvider.areAllTasksCompletedForDate(today);
        expect(allCompleted, true);

        // Update streak
        await streakManager.updateDayCompletion(
          taskProvider.getTasksForDate(today).length,
          taskProvider.getCompletedTasksForDate(today).length,
          allCompleted,
        );

        // Verify streak is earned
        final currentStreak = await streakManager.getCurrentStreak();
        expect(currentStreak, greaterThan(0));
      });

      test('Day does not earn streak when not all tasks are completed', () async {
        // Create tasks for today
        final today = DateTime.now();
        final task1 = Task(
          id: '1',
          title: 'Task 1',
          description: 'Test task 1',
          priority: TaskPriority.medium,
          createdAt: today,
          isCompleted: true,
          completedAt: today,
          priorityIndex: 0,
        );
        final task2 = Task(
          id: '2',
          title: 'Task 2',
          description: 'Test task 2',
          priority: TaskPriority.medium,
          createdAt: today,
          isCompleted: false, // Not completed
          priorityIndex: 0,
        );

        // Add tasks to provider
        await taskProvider.addTask(task1);
        await taskProvider.addTask(task2);

        // Mark only one as completed
        await taskProvider.markTaskAsCompleted(task1);

        // Check if all tasks for today are completed
        final allCompleted = taskProvider.areAllTasksCompletedForDate(today);
        expect(allCompleted, false);

        // Update streak
        await streakManager.updateDayCompletion(
          taskProvider.getTasksForDate(today).length,
          taskProvider.getCompletedTasksForDate(today).length,
          allCompleted,
        );

        // Verify no streak is earned
        final currentStreak = await streakManager.getCurrentStreak();
        expect(currentStreak, equals(0));
      });

      test('Day does not earn streak when no tasks exist', () async {
        final today = DateTime.now();
        
        // No tasks for today
        final allCompleted = taskProvider.areAllTasksCompletedForDate(today);
        expect(allCompleted, false);

        // Update streak
        await streakManager.updateDayCompletion(
          taskProvider.getTasksForDate(today).length,
          taskProvider.getCompletedTasksForDate(today).length,
          allCompleted,
        );

        // Verify no streak is earned
        final currentStreak = await streakManager.getCurrentStreak();
        expect(currentStreak, equals(0));
      });
    });

    group('Streak Breaking Rules', () {
      test('Completing overdue tasks should not count toward today\'s streak', () async {
        final today = DateTime.now();
        final yesterday = today.subtract(Duration(days: 1));

        // Create an overdue task (from yesterday)
        final overdueTask = Task(
          id: '1',
          title: 'Overdue Task',
          description: 'Task from yesterday',
          priority: TaskPriority.medium,
          createdAt: yesterday,
          isCompleted: false,
          priorityIndex: 0,
        );

        // Create today's task
        final todayTask = Task(
          id: '2',
          title: 'Today Task',
          description: 'Task for today',
          priority: TaskPriority.medium,
          createdAt: today,
          isCompleted: false,
          priorityIndex: 0,
        );

        // Add tasks
        await taskProvider.addTask(overdueTask);
        await taskProvider.addTask(todayTask);

        // Complete both tasks today
        await taskProvider.markTaskAsCompleted(overdueTask);
        await taskProvider.markTaskAsCompleted(todayTask);

        // Check today's completion status (should be true since today's task is completed)
        final todayCompleted = taskProvider.areAllTasksCompletedForDate(today);
        expect(todayCompleted, true);

        // Check yesterday's completion status (should be true since overdue task is completed)
        final yesterdayCompleted = taskProvider.areAllTasksCompletedForDate(yesterday);
        expect(yesterdayCompleted, true);

        // Update streak for today
        await streakManager.updateDayCompletion(
          taskProvider.getTasksForDate(today).length,
          taskProvider.getCompletedTasksForDate(today).length,
          todayCompleted,
        );

        // Today should earn a streak because its own tasks are completed
        final currentStreak = await streakManager.getCurrentStreak();
        expect(currentStreak, greaterThan(0));
      });

      test('Adding new task should remove existing streak for that day', () async {
        final today = DateTime.now();

        // Create and complete a task for today
        final task1 = Task(
          id: '1',
          title: 'Task 1',
          description: 'First task',
          priority: TaskPriority.medium,
          createdAt: today,
          isCompleted: true,
          completedAt: today,
          priorityIndex: 0,
        );

        await taskProvider.addTask(task1);
        await taskProvider.markTaskAsCompleted(task1);

        // Update streak (should earn streak)
        await streakManager.updateDayCompletion(
          taskProvider.getTasksForDate(today).length,
          taskProvider.getCompletedTasksForDate(today).length,
          taskProvider.areAllTasksCompletedForDate(today),
        );

        // Verify streak is earned
        final initialStreak = await streakManager.getCurrentStreak();
        expect(initialStreak, greaterThan(0));

        // Add a new task for today (this should remove the streak)
        final task2 = Task(
          id: '2',
          title: 'Task 2',
          description: 'Second task',
          priority: TaskPriority.medium,
          createdAt: today,
          isCompleted: false,
          priorityIndex: 0,
        );

        await taskProvider.addTask(task2);

        // Check that all tasks are no longer completed
        final allCompleted = taskProvider.areAllTasksCompletedForDate(today);
        expect(allCompleted, false);

        // Update streak (should lose streak)
        await streakManager.updateDayCompletion(
          taskProvider.getTasksForDate(today).length,
          taskProvider.getCompletedTasksForDate(today).length,
          allCompleted,
        );

        // Verify streak is lost
        final newStreak = await streakManager.getCurrentStreak();
        expect(newStreak, equals(0));
      });
    });

    group('Task Date Filtering', () {
      test('getTasksForDate returns only tasks for specific date', () async {
        final today = DateTime.now();
        final yesterday = today.subtract(Duration(days: 1));
        final tomorrow = today.add(Duration(days: 1));

        // Create tasks for different dates
        final todayTask = Task(
          id: '1',
          title: 'Today Task',
          description: 'Task for today',
          priority: TaskPriority.medium,
          createdAt: today,
          isCompleted: false,
          priorityIndex: 0,
        );

        final yesterdayTask = Task(
          id: '2',
          title: 'Yesterday Task',
          description: 'Task for yesterday',
          priority: TaskPriority.medium,
          createdAt: yesterday,
          isCompleted: false,
          priorityIndex: 0,
        );

        final tomorrowTask = Task(
          id: '3',
          title: 'Tomorrow Task',
          description: 'Task for tomorrow',
          priority: TaskPriority.medium,
          createdAt: tomorrow,
          isCompleted: false,
          priorityIndex: 0,
        );

        // Add all tasks
        await taskProvider.addTask(todayTask);
        await taskProvider.addTask(yesterdayTask);
        await taskProvider.addTask(tomorrowTask);

        // Test filtering
        final todayTasks = taskProvider.getTasksForDate(today);
        final yesterdayTasks = taskProvider.getTasksForDate(yesterday);
        final tomorrowTasks = taskProvider.getTasksForDate(tomorrow);

        expect(todayTasks.length, equals(1));
        expect(todayTasks.first.id, equals('1'));

        expect(yesterdayTasks.length, equals(1));
        expect(yesterdayTasks.first.id, equals('2'));

        expect(tomorrowTasks.length, equals(1));
        expect(tomorrowTasks.first.id, equals('3'));
      });
    });
  });
}
