import 'package:flutter_test/flutter_test.dart';
import 'package:unstack/database/sql_database.dart';
import 'package:unstack/models/tasks/task.model.dart';
import '../helpers/test_helpers.dart';
import '../helpers/test_constants.dart';

void main() {
  group('Database Integration Tests', () {
    late DatabaseService databaseService;

    setUpAll(() async {
      // Initialize database service for testing
      databaseService = DatabaseService.instance;
    });

    setUp(() async {
      // Clean database before each test
      await databaseService.deleteAllTasks();
      await databaseService.deleteStreakData();
    });

    tearDown(() async {
      // Clean up after each test
      await databaseService.deleteAllTasks();
      await databaseService.deleteStreakData();
    });

    group('Task Database Operations', () {
      test('should insert and retrieve task', () async {
        final task = TestHelpers.createTestTask(
          id: 'test_task_1',
          title: 'Database Test Task',
          description: 'Testing database operations',
        );

        await databaseService.insertTask(task);
        final retrievedTasks = await databaseService.getTasks();

        expect(retrievedTasks.length, 1);
        expect(retrievedTasks.first.id, 'test_task_1');
        expect(retrievedTasks.first.title, 'Database Test Task');
        expect(retrievedTasks.first.description, 'Testing database operations');
      });

      test('should update task in database', () async {
        final originalTask = TestHelpers.createTestTask(
          id: 'test_task_1',
          title: 'Original Title',
          isCompleted: false,
        );

        await databaseService.insertTask(originalTask);

        final updatedTask = originalTask.copyWith(
          title: 'Updated Title',
          isCompleted: true,
          completedAt: DateTime.now(),
        );

        await databaseService.updateTask(updatedTask);
        final completedTasks = await databaseService.getCompletedTasks();

        expect(completedTasks.length, 1);
        expect(completedTasks.first.title, 'Updated Title');
        expect(completedTasks.first.isCompleted, true);
        expect(completedTasks.first.completedAt, isNotNull);
      });

      test('should delete task from database', () async {
        final task = TestHelpers.createTestTask(id: 'test_task_1');
        await databaseService.insertTask(task);

        final tasksBeforeDelete = await databaseService.getTasks();
        expect(tasksBeforeDelete.length, 1);

        await databaseService.deleteTask('test_task_1');

        final tasksAfterDelete = await databaseService.getTasks();
        expect(tasksAfterDelete.length, 0);
      });

      test('should handle multiple task operations', () async {
        final tasks = TestHelpers.createTestTasksForDate(
          DateTime.now(),
          count: 5,
        );

        // Insert all tasks
        for (final task in tasks) {
          await databaseService.insertTask(task);
        }

        final retrievedTasks = await databaseService.getTasks();
        expect(retrievedTasks.length, 5);

        // Update some tasks to completed
        for (int i = 0; i < 3; i++) {
          final updatedTask = tasks[i].copyWith(
            isCompleted: true,
            completedAt: DateTime.now(),
          );
          await databaseService.updateTask(updatedTask);
        }

        final incompleteTasks = await databaseService.getTasks();
        final completedTasks = await databaseService.getCompletedTasks();

        expect(incompleteTasks.length, 2);
        expect(completedTasks.length, 3);
      });

      test('should delete all tasks', () async {
        final tasks = TestHelpers.createTestTasksForDate(
          DateTime.now(),
          count: 10,
        );

        for (final task in tasks) {
          await databaseService.insertTask(task);
        }

        final tasksBeforeDelete = await databaseService.getTasks();
        expect(tasksBeforeDelete.length, 10);

        await databaseService.deleteAllTasks();

        final tasksAfterDelete = await databaseService.getTasks();
        expect(tasksAfterDelete.length, 0);
      });
    });

    group('Streak Database Operations', () {
      test('should insert and retrieve streak data', () async {
        final today = DateTime.now();
        final streakData = {
          'date': today.toIso8601String().split('T')[0],
          'totalTasks': 3,
          'completedTasks': 3,
          'allTasksCompleted': 1,
          'currentStreak': 1,
          'longestStreak': 1,
        };

        await databaseService.insertOrUpdateStreakData(streakData);
        final history = await databaseService.getStreakHistory();

        expect(history.length, 1);
        expect(history.first['totalTasks'], 3);
        expect(history.first['completedTasks'], 3);
        expect(history.first['allTasksCompleted'], 1);
      });

      test('should update existing streak data', () async {
        final today = DateTime.now();
        final dateString = today.toIso8601String().split('T')[0];
        
        final initialData = {
          'date': dateString,
          'totalTasks': 2,
          'completedTasks': 2,
          'allTasksCompleted': 1,
          'currentStreak': 1,
          'longestStreak': 1,
        };

        await databaseService.insertOrUpdateStreakData(initialData);

        final updatedData = {
          'date': dateString,
          'totalTasks': 3,
          'completedTasks': 2,
          'allTasksCompleted': 0,
          'currentStreak': 0,
          'longestStreak': 1,
        };

        await databaseService.insertOrUpdateStreakData(updatedData);
        final history = await databaseService.getStreakHistory();

        expect(history.length, 1);
        expect(history.first['totalTasks'], 3);
        expect(history.first['completedTasks'], 2);
        expect(history.first['allTasksCompleted'], 0);
      });

      test('should delete streak data for specific date', () async {
        final today = DateTime.now();
        final yesterday = today.subtract(Duration(days: 1));
        
        final todayData = {
          'date': today.toIso8601String().split('T')[0],
          'totalTasks': 3,
          'completedTasks': 3,
          'allTasksCompleted': 1,
          'currentStreak': 2,
          'longestStreak': 2,
        };

        final yesterdayData = {
          'date': yesterday.toIso8601String().split('T')[0],
          'totalTasks': 2,
          'completedTasks': 2,
          'allTasksCompleted': 1,
          'currentStreak': 1,
          'longestStreak': 1,
        };

        await databaseService.insertOrUpdateStreakData(todayData);
        await databaseService.insertOrUpdateStreakData(yesterdayData);

        final historyBefore = await databaseService.getStreakHistory();
        expect(historyBefore.length, 2);

        await databaseService.deleteStreakDataForDate(
          yesterday.toIso8601String().split('T')[0],
        );

        final historyAfter = await databaseService.getStreakHistory();
        expect(historyAfter.length, 1);
        expect(historyAfter.first['date'], today.toIso8601String().split('T')[0]);
      });

      test('should get current streak from database', () async {
        final today = DateTime.now();
        final streakData = {
          'date': today.toIso8601String().split('T')[0],
          'totalTasks': 3,
          'completedTasks': 3,
          'allTasksCompleted': 1,
          'currentStreak': 5,
          'longestStreak': 10,
        };

        await databaseService.insertOrUpdateStreakData(streakData);
        final currentStreak = await databaseService.getCurrentStreakFromDB();

        expect(currentStreak, 5);
      });

      test('should get longest streak from database', () async {
        final dates = [
          DateTime.now().subtract(Duration(days: 2)),
          DateTime.now().subtract(Duration(days: 1)),
          DateTime.now(),
        ];

        final streakValues = [3, 7, 5]; // Different longest streak values

        for (int i = 0; i < dates.length; i++) {
          final streakData = {
            'date': dates[i].toIso8601String().split('T')[0],
            'totalTasks': 3,
            'completedTasks': 3,
            'allTasksCompleted': 1,
            'currentStreak': i + 1,
            'longestStreak': streakValues[i],
          };
          await databaseService.insertOrUpdateStreakData(streakData);
        }

        final longestStreak = await databaseService.getLongestStreakFromDB();
        expect(longestStreak, 7); // Should be the maximum value
      });

      test('should get total completed days from database', () async {
        final dates = [
          DateTime.now().subtract(Duration(days: 4)),
          DateTime.now().subtract(Duration(days: 3)),
          DateTime.now().subtract(Duration(days: 2)),
          DateTime.now().subtract(Duration(days: 1)),
          DateTime.now(),
        ];

        final completionStates = [1, 0, 1, 1, 0]; // 3 completed days

        for (int i = 0; i < dates.length; i++) {
          final streakData = {
            'date': dates[i].toIso8601String().split('T')[0],
            'totalTasks': 3,
            'completedTasks': completionStates[i] == 1 ? 3 : 2,
            'allTasksCompleted': completionStates[i],
            'currentStreak': 1,
            'longestStreak': 1,
          };
          await databaseService.insertOrUpdateStreakData(streakData);
        }

        final totalCompleted = await databaseService.getTotalCompletedDaysFromDB();
        expect(totalCompleted, 3);
      });

      test('should update streak counters', () async {
        final today = DateTime.now();
        final initialData = {
          'date': today.toIso8601String().split('T')[0],
          'totalTasks': 3,
          'completedTasks': 3,
          'allTasksCompleted': 1,
          'currentStreak': 1,
          'longestStreak': 1,
        };

        await databaseService.insertOrUpdateStreakData(initialData);

        await databaseService.updateStreakCounters(5, 10);

        final currentStreak = await databaseService.getCurrentStreakFromDB();
        final longestStreak = await databaseService.getLongestStreakFromDB();

        expect(currentStreak, 5);
        expect(longestStreak, 10);
      });

      test('should delete all streak data', () async {
        final dates = List.generate(
          5,
          (i) => DateTime.now().subtract(Duration(days: i)),
        );

        for (final date in dates) {
          final streakData = {
            'date': date.toIso8601String().split('T')[0],
            'totalTasks': 3,
            'completedTasks': 3,
            'allTasksCompleted': 1,
            'currentStreak': 1,
            'longestStreak': 1,
          };
          await databaseService.insertOrUpdateStreakData(streakData);
        }

        final historyBefore = await databaseService.getStreakHistory();
        expect(historyBefore.length, 5);

        await databaseService.deleteStreakData();

        final historyAfter = await databaseService.getStreakHistory();
        expect(historyAfter.length, 0);
      });
    });

    group('Data Persistence and Recovery', () {
      test('should persist data across database operations', () async {
        // Add tasks and streaks
        final task = TestHelpers.createTestTask(id: 'persistent_task');
        await databaseService.insertTask(task);

        final streakData = {
          'date': DateTime.now().toIso8601String().split('T')[0],
          'totalTasks': 1,
          'completedTasks': 1,
          'allTasksCompleted': 1,
          'currentStreak': 1,
          'longestStreak': 1,
        };
        await databaseService.insertOrUpdateStreakData(streakData);

        // Verify data exists
        final tasks = await databaseService.getTasks();
        final streaks = await databaseService.getStreakHistory();

        expect(tasks.length, 1);
        expect(streaks.length, 1);
        expect(tasks.first.id, 'persistent_task');
      });

      test('should handle concurrent database operations', () async {
        final tasks = List.generate(
          10,
          (i) => TestHelpers.createTestTask(id: 'concurrent_task_$i'),
        );

        // Perform concurrent insertions
        final futures = tasks.map((task) => databaseService.insertTask(task));
        await Future.wait(futures);

        final retrievedTasks = await databaseService.getTasks();
        expect(retrievedTasks.length, 10);
      });
    });
  });
}
