import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:unstack/providers/task_provider.dart';
import 'package:unstack/providers/streak_provider.dart';
import 'package:unstack/models/tasks/task.model.dart';
import '../helpers/test_helpers.dart';
import '../helpers/test_constants.dart';

void main() {
  group('Provider Integration Tests', () {
    late TaskProvider taskProvider;
    late StreakProvider streakProvider;

    setUp(() {
      TestHelpers.resetTestData();
      taskProvider = TaskProvider();
      streakProvider = StreakProvider();
    });

    tearDown(() {
      TestHelpers.resetTestData();
    });

    group('TaskProvider and StreakProvider Interaction', () {
      test('should update streak when all today\'s tasks are completed', () async {
        final today = DateTime.now();
        final tasks = TestHelpers.createTestTasksForDate(today, count: 3);

        // Add tasks
        for (final task in tasks) {
          await taskProvider.addTask(task);
        }

        expect(taskProvider.todayTasks.length, 3);
        expect(taskProvider.todaysTasksCompleted, false);

        // Complete all tasks
        for (final task in tasks) {
          await taskProvider.markTaskAsCompleted(task);
        }

        expect(taskProvider.todaysTasksCompleted, true);

        // Update streak based on task completion
        await streakProvider.updateStreakFromTaskProvider(taskProvider, today);

        // Streak should be updated
        expect(streakProvider.currentStreak, greaterThan(0));
      });

      test('should not update streak when tasks are incomplete', () async {
        final today = DateTime.now();
        final tasks = TestHelpers.createTestTasksForDate(today, count: 3);

        // Add tasks
        for (final task in tasks) {
          await taskProvider.addTask(task);
        }

        // Complete only 2 out of 3 tasks
        await taskProvider.markTaskAsCompleted(tasks[0]);
        await taskProvider.markTaskAsCompleted(tasks[1]);

        expect(taskProvider.todaysTasksCompleted, false);

        // Update streak based on task completion
        await streakProvider.updateStreakFromTaskProvider(taskProvider, today);

        // Streak should remain 0
        expect(streakProvider.currentStreak, 0);
      });

      test('should remove streak when new task is added to completed day', () async {
        final today = DateTime.now();
        final initialTasks = TestHelpers.createTestTasksForDate(today, count: 2);

        // Add and complete initial tasks
        for (final task in initialTasks) {
          await taskProvider.addTask(task);
          await taskProvider.markTaskAsCompleted(task);
        }

        expect(taskProvider.todaysTasksCompleted, true);

        // Update streak
        await streakProvider.updateStreakFromTaskProvider(taskProvider, today);
        expect(streakProvider.currentStreak, greaterThan(0));

        // Add new task to the same day
        final newTask = TestHelpers.createTestTask(
          id: 'new_task',
          createdAt: today,
        );
        await taskProvider.addTask(newTask);

        expect(taskProvider.todaysTasksCompleted, false);

        // Update streak again
        await streakProvider.updateStreakFromTaskProvider(taskProvider, today);

        // Streak should be reset
        expect(streakProvider.currentStreak, 0);
      });

      test('should handle overdue task completion correctly', () async {
        final today = DateTime.now();
        final yesterday = today.subtract(Duration(days: 1));

        // Create tasks for different days
        final todayTask = TestHelpers.createTestTask(
          id: 'today_task',
          createdAt: today,
        );
        final overdueTask = TestHelpers.createTestTask(
          id: 'overdue_task',
          createdAt: yesterday,
        );

        await taskProvider.addTask(todayTask);
        await taskProvider.addTask(overdueTask);

        // Complete both tasks today
        await taskProvider.markTaskAsCompleted(todayTask);
        await taskProvider.markTaskAsCompleted(overdueTask);

        // Today's tasks should be completed
        expect(taskProvider.areAllTasksCompletedForDate(today), true);
        // Yesterday's tasks should also be completed
        expect(taskProvider.areAllTasksCompletedForDate(yesterday), true);

        // Update streaks for both days
        await streakProvider.updateStreakFromTaskProvider(taskProvider, today);
        await streakProvider.updateStreakFromTaskProvider(taskProvider, yesterday);

        // Both days should contribute to streak
        expect(streakProvider.currentStreak, greaterThan(0));
      });
    });

    group('Real-time State Updates', () {
      testWidgets('should notify listeners when task state changes', (WidgetTester tester) async {
        int taskProviderNotifications = 0;
        int streakProviderNotifications = 0;

        await tester.pumpWidget(
          MultiProvider(
            providers: [
              ChangeNotifierProvider<TaskProvider>.value(value: taskProvider),
              ChangeNotifierProvider<StreakProvider>.value(value: streakProvider),
            ],
            child: MaterialApp(
              home: Scaffold(
                body: Column(
                  children: [
                    Consumer<TaskProvider>(
                      builder: (context, provider, child) {
                        taskProviderNotifications++;
                        return Text('Tasks: ${provider.tasks.length}');
                      },
                    ),
                    Consumer<StreakProvider>(
                      builder: (context, provider, child) {
                        streakProviderNotifications++;
                        return Text('Streak: ${provider.currentStreak}');
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        );

        final initialTaskNotifications = taskProviderNotifications;
        final initialStreakNotifications = streakProviderNotifications;

        // Add a task
        final task = TestHelpers.createTestTask();
        await taskProvider.addTask(task);
        await tester.pump();

        // TaskProvider should notify listeners
        expect(taskProviderNotifications, greaterThan(initialTaskNotifications));

        // Complete the task
        await taskProvider.markTaskAsCompleted(task);
        await tester.pump();

        // Should trigger more notifications
        expect(taskProviderNotifications, greaterThan(initialTaskNotifications + 1));
      });

      testWidgets('should update UI when streak changes', (WidgetTester tester) async {
        await tester.pumpWidget(
          MultiProvider(
            providers: [
              ChangeNotifierProvider<TaskProvider>.value(value: taskProvider),
              ChangeNotifierProvider<StreakProvider>.value(value: streakProvider),
            ],
            child: MaterialApp(
              home: Scaffold(
                body: Consumer<StreakProvider>(
                  builder: (context, provider, child) {
                    return Text('Current Streak: ${provider.currentStreak}');
                  },
                ),
              ),
            ),
          ),
        );

        expect(find.text('Current Streak: 0'), findsOneWidget);

        // Update streak
        await streakProvider.updateStreakForToday(3, 3, true);
        await tester.pump();

        // UI should update
        expect(find.textContaining('Current Streak:'), findsOneWidget);
      });
    });

    group('State Synchronization', () {
      test('should maintain consistency between providers', () async {
        final today = DateTime.now();
        final tasks = TestHelpers.createTestTasksForDate(today, count: 5);

        // Add tasks through TaskProvider
        for (final task in tasks) {
          await taskProvider.addTask(task);
        }

        // Complete 3 tasks
        for (int i = 0; i < 3; i++) {
          await taskProvider.markTaskAsCompleted(tasks[i]);
        }

        // Sync with StreakProvider
        await streakProvider.updateStreakFromTaskProvider(taskProvider, today);

        // Verify consistency
        expect(taskProvider.getTasksForDate(today).length, 5);
        expect(taskProvider.getCompletedTasksForDate(today).length, 3);
        expect(taskProvider.areAllTasksCompletedForDate(today), false);
        expect(streakProvider.currentStreak, 0);

        // Complete remaining tasks
        for (int i = 3; i < 5; i++) {
          await taskProvider.markTaskAsCompleted(tasks[i]);
        }

        // Sync again
        await streakProvider.updateStreakFromTaskProvider(taskProvider, today);

        // Now should be consistent
        expect(taskProvider.areAllTasksCompletedForDate(today), true);
        expect(streakProvider.currentStreak, greaterThan(0));
      });

      test('should handle concurrent operations correctly', () async {
        final today = DateTime.now();
        final tasks = List.generate(
          10,
          (i) => TestHelpers.createTestTask(id: 'concurrent_task_$i', createdAt: today),
        );

        // Concurrent task additions
        final addFutures = tasks.map((task) => taskProvider.addTask(task));
        await Future.wait(addFutures);

        expect(taskProvider.tasks.length, 10);

        // Concurrent task completions
        final completeFutures = tasks.map((task) => taskProvider.markTaskAsCompleted(task));
        await Future.wait(completeFutures);

        expect(taskProvider.completedTasks.length, 10);
        expect(taskProvider.todaysTasksCompleted, true);

        // Update streak
        await streakProvider.updateStreakFromTaskProvider(taskProvider, today);
        expect(streakProvider.currentStreak, greaterThan(0));
      });
    });

    group('Error Handling and Recovery', () {
      test('should handle provider errors gracefully', () async {
        // Simulate error in TaskProvider
        final invalidTask = TestHelpers.createTestTask(id: '');
        await taskProvider.addTask(invalidTask);

        // Should not crash StreakProvider
        await streakProvider.updateStreakFromTaskProvider(taskProvider, DateTime.now());

        // Both providers should remain functional
        expect(taskProvider.hasError, false);
        expect(streakProvider.hasError, false);
      });

      test('should recover from inconsistent state', () async {
        final today = DateTime.now();
        
        // Create inconsistent state
        final task = TestHelpers.createTestTask(createdAt: today);
        await taskProvider.addTask(task);
        await taskProvider.markTaskAsCompleted(task);

        // Manually set incorrect streak
        await streakProvider.updateStreakForToday(0, 0, false);

        // Resync should fix inconsistency
        await streakProvider.updateStreakFromTaskProvider(taskProvider, today);

        // Should now be consistent
        expect(taskProvider.todaysTasksCompleted, true);
        expect(streakProvider.currentStreak, greaterThan(0));
      });
    });

    group('Performance and Memory', () {
      test('should handle large datasets efficiently', () async {
        final today = DateTime.now();
        final largeTasks = List.generate(
          TestConstants.largeDataSet,
          (i) => TestHelpers.createTestTask(
            id: 'large_task_$i',
            createdAt: today.subtract(Duration(days: i % 30)),
          ),
        );

        final stopwatch = Stopwatch()..start();

        // Add all tasks
        for (final task in largeTasks) {
          await taskProvider.addTask(task);
        }

        stopwatch.stop();
        expect(stopwatch.elapsed, lessThan(TestConstants.maxDatabaseOperationTime * largeTasks.length));

        // Verify data integrity
        expect(taskProvider.tasks.length, TestConstants.largeDataSet);
      });

      test('should not leak memory with frequent updates', () async {
        final today = DateTime.now();

        // Simulate frequent updates
        for (int i = 0; i < 100; i++) {
          final task = TestHelpers.createTestTask(
            id: 'memory_test_$i',
            createdAt: today,
          );

          await taskProvider.addTask(task);
          await taskProvider.markTaskAsCompleted(task);
          await streakProvider.updateStreakFromTaskProvider(taskProvider, today);
          await taskProvider.deleteTask(task.id);
        }

        // Should complete without memory issues
        expect(taskProvider.tasks.length, 0);
        expect(taskProvider.completedTasks.length, 0);
      });
    });

    group('Edge Cases', () {
      test('should handle date boundary conditions', () async {
        final midnight = DateTime.now();
        final justBeforeMidnight = midnight.subtract(Duration(milliseconds: 1));
        final justAfterMidnight = midnight.add(Duration(milliseconds: 1));

        final taskBefore = TestHelpers.createTestTask(
          id: 'before_midnight',
          createdAt: justBeforeMidnight,
        );
        final taskAfter = TestHelpers.createTestTask(
          id: 'after_midnight',
          createdAt: justAfterMidnight,
        );

        await taskProvider.addTask(taskBefore);
        await taskProvider.addTask(taskAfter);

        // Should handle date boundaries correctly
        final beforeTasks = taskProvider.getTasksForDate(justBeforeMidnight);
        final afterTasks = taskProvider.getTasksForDate(justAfterMidnight);

        expect(beforeTasks.length, 1);
        expect(afterTasks.length, 1);
        expect(beforeTasks.first.id, 'before_midnight');
        expect(afterTasks.first.id, 'after_midnight');
      });

      test('should handle empty state correctly', () async {
        // Both providers start empty
        expect(taskProvider.tasks.isEmpty, true);
        expect(taskProvider.completedTasks.isEmpty, true);
        expect(streakProvider.currentStreak, 0);

        // Operations on empty state should not crash
        await streakProvider.updateStreakFromTaskProvider(taskProvider, DateTime.now());
        
        expect(streakProvider.currentStreak, 0);
      });
    });
  });
}
