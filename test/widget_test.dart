import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:unstack/main.dart';
import 'package:unstack/providers/task_provider.dart';
import 'package:unstack/providers/streak_provider.dart';
import 'helpers/test_helpers.dart';

void main() {
  group('Main App Widget Tests', () {
    setUp(() {
      TestHelpers.resetTestData();
    });

    tearDown(() {
      TestHelpers.resetTestData();
    });

    testWidgets('should render main app without errors',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<TaskProvider>(create: (_) => TaskProvider()),
            ChangeNotifierProvider<StreakProvider>(
                create: (_) => StreakProvider()),
          ],
          child: const MyApp(),
        ),
      );

      // Should render without errors
      expect(find.byType(MyApp), findsOneWidget);

      // Wait for any async operations to complete
      await tester.pumpAndSettle();
    });

    testWidgets('should navigate through app screens',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<TaskProvider>(create: (_) => TaskProvider()),
            ChangeNotifierProvider<StreakProvider>(
                create: (_) => StreakProvider()),
          ],
          child: const MyApp(),
        ),
      );

      await tester.pumpAndSettle();

      // Should be able to navigate through the app
      // Note: Specific navigation tests depend on app structure
      expect(find.byType(MyApp), findsOneWidget);
    });

    testWidgets('should handle provider state changes',
        (WidgetTester tester) async {
      final taskProvider = TaskProvider();
      final streakProvider = StreakProvider();

      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<TaskProvider>.value(value: taskProvider),
            ChangeNotifierProvider<StreakProvider>.value(value: streakProvider),
          ],
          child: const MyApp(),
        ),
      );

      await tester.pumpAndSettle();

      // Add a task and verify UI updates
      final task = TestHelpers.createTestTask();
      await taskProvider.addTask(task);
      await tester.pump();

      // Should handle state changes without errors
      expect(find.byType(MyApp), findsOneWidget);
    });
  });
}
