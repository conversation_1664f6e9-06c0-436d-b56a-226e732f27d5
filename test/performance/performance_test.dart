import 'package:flutter_test/flutter_test.dart';
import 'package:unstack/providers/task_provider.dart';
import 'package:unstack/providers/streak_provider.dart';
import '../helpers/test_helpers.dart';
import '../helpers/test_constants.dart';

void main() {
  group('Performance Tests', () {
    late TaskProvider taskProvider;
    late StreakProvider streakProvider;

    setUp(() {
      TestHelpers.resetTestData();
      taskProvider = TaskProvider();
      streakProvider = StreakProvider();
    });

    tearDown(() {
      TestHelpers.resetTestData();
    });

    group('Task Operations Performance', () {
      test('should add tasks within performance threshold', () async {
        final tasks = List.generate(
          TestConstants.mediumDataSet,
          (i) => TestHelpers.createTestTask(id: 'perf_task_$i'),
        );

        final stopwatch = Stopwatch()..start();

        for (final task in tasks) {
          await taskProvider.addTask(task);
        }

        stopwatch.stop();

        final averageTime = stopwatch.elapsed.inMicroseconds / tasks.length;
        expect(
          Duration(microseconds: averageTime.round()),
          lessThan(TestConstants.maxDatabaseOperationTime),
        );
        expect(taskProvider.tasks.length, TestConstants.mediumDataSet);
      });

      test('should complete tasks within performance threshold', () async {
        final tasks = List.generate(
          TestConstants.mediumDataSet,
          (i) => TestHelpers.createTestTask(id: 'completion_perf_$i'),
        );

        // Add all tasks first
        for (final task in tasks) {
          await taskProvider.addTask(task);
        }

        final stopwatch = Stopwatch()..start();

        for (final task in tasks) {
          await taskProvider.markTaskAsCompleted(task);
        }

        stopwatch.stop();

        final averageTime = stopwatch.elapsed.inMicroseconds / tasks.length;
        expect(
          Duration(microseconds: averageTime.round()),
          lessThan(TestConstants.maxDatabaseOperationTime),
        );
        expect(taskProvider.completedTasks.length, TestConstants.mediumDataSet);
      });

      test('should delete tasks within performance threshold', () async {
        final tasks = List.generate(
          TestConstants.mediumDataSet,
          (i) => TestHelpers.createTestTask(id: 'delete_perf_$i'),
        );

        // Add all tasks first
        for (final task in tasks) {
          await taskProvider.addTask(task);
        }

        final stopwatch = Stopwatch()..start();

        for (final task in tasks) {
          await taskProvider.deleteTask(task.id);
        }

        stopwatch.stop();

        final averageTime = stopwatch.elapsed.inMicroseconds / tasks.length;
        expect(
          Duration(microseconds: averageTime.round()),
          lessThan(TestConstants.maxDatabaseOperationTime),
        );
        expect(taskProvider.tasks.length, 0);
      });

      test('should filter tasks efficiently with large dataset', () async {
        final today = DateTime.now();
        final yesterday = today.subtract(Duration(days: 1));
        final tomorrow = today.add(Duration(days: 1));

        final dates = [today, yesterday, tomorrow];
        final allTasks = <Task>[];

        // Create large dataset across multiple dates
        for (final date in dates) {
          final tasksForDate = TestHelpers.createTestTasksForDate(
            date,
            count: TestConstants.mediumDataSet ~/ 3,
          );
          allTasks.addAll(tasksForDate);
        }

        // Add all tasks
        for (final task in allTasks) {
          await taskProvider.addTask(task);
        }

        final stopwatch = Stopwatch()..start();

        // Filter tasks for each date
        for (final date in dates) {
          final filteredTasks = taskProvider.getTasksForDate(date);
          expect(filteredTasks.length, TestConstants.mediumDataSet ~/ 3);
        }

        stopwatch.stop();

        expect(stopwatch.elapsed, lessThan(TestConstants.maxProviderUpdateTime));
      });
    });

    group('Streak Operations Performance', () {
      test('should update streak data within performance threshold', () async {
        final dates = List.generate(
          TestConstants.mediumDataSet,
          (i) => DateTime.now().subtract(Duration(days: i)),
        );

        final stopwatch = Stopwatch()..start();

        for (final date in dates) {
          await streakProvider.updateStreakForDate(date, 3, 3, true);
        }

        stopwatch.stop();

        final averageTime = stopwatch.elapsed.inMicroseconds / dates.length;
        expect(
          Duration(microseconds: averageTime.round()),
          lessThan(TestConstants.maxDatabaseOperationTime),
        );
      });

      test('should calculate current streak efficiently', () async {
        // Create a long streak history
        final today = DateTime.now();
        for (int i = 0; i < TestConstants.mediumDataSet; i++) {
          final date = today.subtract(Duration(days: i));
          await streakProvider.updateStreakForDate(date, 1, 1, true);
        }

        final stopwatch = Stopwatch()..start();

        await streakProvider.loadStreakData();
        final currentStreak = streakProvider.currentStreak;

        stopwatch.stop();

        expect(currentStreak, greaterThan(0));
        expect(stopwatch.elapsed, lessThan(TestConstants.maxProviderUpdateTime));
      });

      test('should load streak history efficiently', () async {
        // Create large streak history
        final today = DateTime.now();
        for (int i = 0; i < TestConstants.mediumDataSet; i++) {
          final date = today.subtract(Duration(days: i));
          await streakProvider.updateStreakForDate(
            date,
            3,
            i % 2 == 0 ? 3 : 2, // Alternate between complete and incomplete
            i % 2 == 0,
          );
        }

        final stopwatch = Stopwatch()..start();

        await streakProvider.loadStreakData();
        final history = streakProvider.completionHistory;

        stopwatch.stop();

        expect(history.length, greaterThanOrEqualTo(TestConstants.mediumDataSet));
        expect(stopwatch.elapsed, lessThan(TestConstants.maxProviderUpdateTime));
      });
    });

    group('Memory Usage Performance', () {
      test('should handle large task datasets without excessive memory usage', () async {
        final initialTaskCount = taskProvider.tasks.length;

        // Add large dataset
        for (int i = 0; i < TestConstants.largeDataSet; i++) {
          final task = TestHelpers.createTestTask(id: 'memory_task_$i');
          await taskProvider.addTask(task);
        }

        expect(taskProvider.tasks.length, initialTaskCount + TestConstants.largeDataSet);

        // Clean up
        await taskProvider.deleteAllTasks();
        expect(taskProvider.tasks.length, 0);
      });

      test('should handle frequent provider updates efficiently', () async {
        final stopwatch = Stopwatch()..start();

        // Simulate frequent UI updates
        for (int i = 0; i < 1000; i++) {
          final task = TestHelpers.createTestTask(id: 'frequent_task_$i');
          await taskProvider.addTask(task);
          
          // Simulate provider notification
          taskProvider.notifyListeners();
          
          await taskProvider.deleteTask(task.id);
        }

        stopwatch.stop();

        expect(stopwatch.elapsed, lessThan(TestConstants.longTimeout));
        expect(taskProvider.tasks.length, 0);
      });
    });

    group('Concurrent Operations Performance', () {
      test('should handle concurrent task operations efficiently', () async {
        final tasks = List.generate(
          TestConstants.smallDataSet,
          (i) => TestHelpers.createTestTask(id: 'concurrent_task_$i'),
        );

        final stopwatch = Stopwatch()..start();

        // Concurrent additions
        final addFutures = tasks.map((task) => taskProvider.addTask(task));
        await Future.wait(addFutures);

        // Concurrent completions
        final completeFutures = tasks.map((task) => taskProvider.markTaskAsCompleted(task));
        await Future.wait(completeFutures);

        stopwatch.stop();

        expect(taskProvider.completedTasks.length, TestConstants.smallDataSet);
        expect(stopwatch.elapsed, lessThan(TestConstants.mediumTimeout));
      });

      test('should handle concurrent streak updates efficiently', () async {
        final dates = List.generate(
          TestConstants.smallDataSet,
          (i) => DateTime.now().subtract(Duration(days: i)),
        );

        final stopwatch = Stopwatch()..start();

        // Concurrent streak updates
        final futures = dates.map((date) => 
          streakProvider.updateStreakForDate(date, 1, 1, true)
        );
        await Future.wait(futures);

        stopwatch.stop();

        expect(stopwatch.elapsed, lessThan(TestConstants.mediumTimeout));
      });
    });

    group('Database Performance', () {
      test('should perform batch operations efficiently', () async {
        final tasks = List.generate(
          TestConstants.mediumDataSet,
          (i) => TestHelpers.createTestTask(id: 'batch_task_$i'),
        );

        final stopwatch = Stopwatch()..start();

        // Batch add operations
        for (final task in tasks) {
          await taskProvider.addTask(task);
        }

        // Batch delete operations
        await taskProvider.deleteAllTasks();

        stopwatch.stop();

        expect(stopwatch.elapsed, lessThan(TestConstants.longTimeout));
        expect(taskProvider.tasks.length, 0);
      });

      test('should handle database queries efficiently', () async {
        final today = DateTime.now();
        
        // Create tasks across multiple dates
        for (int i = 0; i < TestConstants.mediumDataSet; i++) {
          final date = today.subtract(Duration(days: i % 30));
          final task = TestHelpers.createTestTask(
            id: 'query_task_$i',
            createdAt: date,
          );
          await taskProvider.addTask(task);
        }

        final stopwatch = Stopwatch()..start();

        // Perform multiple queries
        for (int i = 0; i < 30; i++) {
          final date = today.subtract(Duration(days: i));
          final tasksForDate = taskProvider.getTasksForDate(date);
          expect(tasksForDate, isNotNull);
        }

        stopwatch.stop();

        expect(stopwatch.elapsed, lessThan(TestConstants.maxProviderUpdateTime));
      });
    });

    group('UI Performance Simulation', () {
      test('should handle rapid UI state changes efficiently', () async {
        final task = TestHelpers.createTestTask();
        await taskProvider.addTask(task);

        final stopwatch = Stopwatch()..start();

        // Simulate rapid UI interactions
        for (int i = 0; i < 100; i++) {
          await taskProvider.toggleTaskCompletion(task);
        }

        stopwatch.stop();

        expect(stopwatch.elapsed, lessThan(TestConstants.mediumTimeout));
      });

      test('should handle provider listener notifications efficiently', () async {
        int notificationCount = 0;
        
        taskProvider.addListener(() {
          notificationCount++;
        });

        final stopwatch = Stopwatch()..start();

        // Trigger multiple notifications
        for (int i = 0; i < 100; i++) {
          final task = TestHelpers.createTestTask(id: 'notification_task_$i');
          await taskProvider.addTask(task);
        }

        stopwatch.stop();

        expect(notificationCount, greaterThan(0));
        expect(stopwatch.elapsed, lessThan(TestConstants.mediumTimeout));
      });
    });

    group('Stress Tests', () {
      test('should handle maximum realistic load', () async {
        final today = DateTime.now();
        
        // Simulate maximum realistic daily usage
        final maxDailyTasks = 50;
        final maxDays = 365;

        final stopwatch = Stopwatch()..start();

        // Create tasks for a full year
        for (int day = 0; day < maxDays; day++) {
          final date = today.subtract(Duration(days: day));
          
          for (int taskNum = 0; taskNum < maxDailyTasks; taskNum++) {
            final task = TestHelpers.createTestTask(
              id: 'stress_task_${day}_$taskNum',
              createdAt: date,
            );
            await taskProvider.addTask(task);
            
            // Complete some tasks
            if (taskNum % 2 == 0) {
              await taskProvider.markTaskAsCompleted(task);
            }
          }
          
          // Update streak for the day
          final tasksForDate = taskProvider.getTasksForDate(date);
          final completedForDate = taskProvider.getCompletedTasksForDate(date);
          final allCompleted = taskProvider.areAllTasksCompletedForDate(date);
          
          await streakProvider.updateStreakForDate(
            date,
            tasksForDate.length,
            completedForDate.length,
            allCompleted,
          );
        }

        stopwatch.stop();

        expect(taskProvider.totalTasks, maxDailyTasks * maxDays);
        expect(stopwatch.elapsed, lessThan(Duration(minutes: 5))); // Should complete within 5 minutes
      });
    });
  });
}
