import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:unstack/widgets/streak_widget.dart';
import '../helpers/test_helpers.dart';
import '../helpers/test_constants.dart';

void main() {
  group('StreakWidget Tests', () {
    group('Streak Widget Rendering', () {
      testWidgets('should render streak widget with zero streak', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StreakWidget(
                currentStreak: 0,
                key: Key(TestConstants.streakWidgetKey),
              ),
            ),
          ),
        );

        expect(find.byKey(Key(TestConstants.streakWidgetKey)), findsOneWidget);
        expect(find.text('0 streak'), findsOneWidget);
        expect(find.byType(StreakWidget), findsOneWidget);
      });

      testWidgets('should render streak widget with single streak', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StreakWidget(
                currentStreak: 1,
              ),
            ),
          ),
        );

        expect(find.text('1 streak'), findsOneWidget);
        expect(find.byType(StreakWidget), findsOneWidget);
      });

      testWidgets('should render streak widget with multiple streaks', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StreakWidget(
                currentStreak: 5,
              ),
            ),
          ),
        );

        expect(find.text('5 streaks'), findsOneWidget);
        expect(find.byType(StreakWidget), findsOneWidget);
      });

      testWidgets('should render flame icon', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StreakWidget(
                currentStreak: 3,
              ),
            ),
          ),
        );

        // Should have flame icon
        expect(find.byIcon(Icons.local_fire_department), findsOneWidget);
      });

      testWidgets('should handle very large streak numbers', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StreakWidget(
                currentStreak: 999,
              ),
            ),
          ),
        );

        expect(find.text('999 streaks'), findsOneWidget);
        expect(find.byType(StreakWidget), findsOneWidget);
      });
    });

    group('Streak Widget Visual States', () {
      testWidgets('should show different visual state for zero streak', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StreakWidget(
                currentStreak: 0,
              ),
            ),
          ),
        );

        final widget = tester.widget<StreakWidget>(find.byType(StreakWidget));
        expect(widget.currentStreak, 0);

        // Should have muted appearance for zero streak
        // Exact visual verification depends on implementation
      });

      testWidgets('should show active visual state for positive streak', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StreakWidget(
                currentStreak: 5,
              ),
            ),
          ),
        );

        final widget = tester.widget<StreakWidget>(find.byType(StreakWidget));
        expect(widget.currentStreak, 5);

        // Should have active/highlighted appearance for positive streak
        // Exact visual verification depends on implementation
      });

      testWidgets('should animate between different streak values', (WidgetTester tester) async {
        int currentStreak = 0;

        await tester.pumpWidget(
          StatefulBuilder(
            builder: (context, setState) {
              return MaterialApp(
                home: Scaffold(
                  body: Column(
                    children: [
                      StreakWidget(
                        currentStreak: currentStreak,
                      ),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            currentStreak++;
                          });
                        },
                        child: Text('Increment'),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );

        expect(find.text('0 streak'), findsOneWidget);

        // Increment streak
        await tester.tap(find.text('Increment'));
        await tester.pump();

        // Should start animation
        await tester.pump(Duration(milliseconds: 500));
        
        // Complete animation
        await tester.pumpAndSettle();
        
        expect(find.text('1 streak'), findsOneWidget);
      });
    });

    group('Streak Widget Interactions', () {
      testWidgets('should be tappable and navigate to streak page', (WidgetTester tester) async {
        bool navigationCalled = false;

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StreakWidget(
                currentStreak: 3,
              ),
            ),
            onGenerateRoute: (settings) {
              if (settings.name == '/streak') {
                navigationCalled = true;
                return MaterialPageRoute(
                  builder: (context) => Scaffold(
                    appBar: AppBar(title: Text('Streak Page')),
                    body: Text('Streak Page Content'),
                  ),
                );
              }
              return null;
            },
          ),
        );

        await tester.tap(find.byType(StreakWidget));
        await tester.pumpAndSettle();

        // Navigation should be triggered
        // Note: Actual navigation testing depends on routing implementation
      });

      testWidgets('should provide haptic feedback on tap', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StreakWidget(
                currentStreak: 2,
              ),
            ),
          ),
        );

        await tester.tap(find.byType(StreakWidget));
        await tester.pumpAndSettle();

        // Should trigger haptic feedback (hard to test directly)
        // Verify no errors occur
      });

      testWidgets('should handle rapid taps gracefully', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StreakWidget(
                currentStreak: 1,
              ),
            ),
          ),
        );

        // Rapid taps
        for (int i = 0; i < 10; i++) {
          await tester.tap(find.byType(StreakWidget));
          await tester.pump(Duration(milliseconds: 50));
        }

        await tester.pumpAndSettle();
        
        // Should handle rapid taps without errors
      });
    });

    group('Streak Widget Accessibility', () {
      testWidgets('should have proper accessibility labels', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StreakWidget(
                currentStreak: 7,
              ),
            ),
          ),
        );

        // Should have semantic labels for screen readers
        final semantics = tester.getSemantics(find.byType(StreakWidget));
        expect(semantics.hasAction(SemanticsAction.tap), true);
        
        // Should announce streak count
        expect(find.text('7 streaks'), findsOneWidget);
      });

      testWidgets('should support keyboard navigation', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StreakWidget(
                currentStreak: 4,
              ),
            ),
          ),
        );

        // Should be focusable
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pumpAndSettle();

        // Should handle keyboard activation
        await tester.sendKeyEvent(LogicalKeyboardKey.enter);
        await tester.pumpAndSettle();
      });

      testWidgets('should provide meaningful descriptions for different streak values', (WidgetTester tester) async {
        final streakValues = [0, 1, 5, 10, 100];

        for (final streak in streakValues) {
          await tester.pumpWidget(
            MaterialApp(
              home: Scaffold(
                body: StreakWidget(
                  currentStreak: streak,
                ),
              ),
            ),
          );

          if (streak == 0) {
            expect(find.text('0 streak'), findsOneWidget);
          } else if (streak == 1) {
            expect(find.text('1 streak'), findsOneWidget);
          } else {
            expect(find.text('$streak streaks'), findsOneWidget);
          }
        }
      });
    });

    group('Streak Widget Performance', () {
      testWidgets('should render efficiently with frequent updates', (WidgetTester tester) async {
        int currentStreak = 0;

        await tester.pumpWidget(
          StatefulBuilder(
            builder: (context, setState) {
              return MaterialApp(
                home: Scaffold(
                  body: Column(
                    children: [
                      StreakWidget(
                        currentStreak: currentStreak,
                      ),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            currentStreak = (currentStreak + 1) % 100;
                          });
                        },
                        child: Text('Update'),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );

        // Rapid updates
        for (int i = 0; i < 20; i++) {
          await tester.tap(find.text('Update'));
          await tester.pump(Duration(milliseconds: 16)); // 60 FPS
        }

        await tester.pumpAndSettle();
        
        // Should handle rapid updates efficiently
      });

      testWidgets('should not rebuild unnecessarily', (WidgetTester tester) async {
        int buildCount = 0;

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) {
                  buildCount++;
                  return StreakWidget(
                    currentStreak: 5,
                  );
                },
              ),
            ),
          ),
        );

        final initialBuildCount = buildCount;

        // Pump without changes
        await tester.pump();
        await tester.pump();

        // Build count should not increase unnecessarily
        expect(buildCount, initialBuildCount);
      });
    });

    group('Streak Widget Edge Cases', () {
      testWidgets('should handle negative streak values', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StreakWidget(
                currentStreak: -1,
              ),
            ),
          ),
        );

        // Should handle negative values gracefully
        // Implementation should probably treat negative as zero
        expect(find.byType(StreakWidget), findsOneWidget);
      });

      testWidgets('should handle extremely large streak values', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StreakWidget(
                currentStreak: 999999,
              ),
            ),
          ),
        );

        expect(find.text('999999 streaks'), findsOneWidget);
        expect(find.byType(StreakWidget), findsOneWidget);
      });

      testWidgets('should maintain state during parent rebuilds', (WidgetTester tester) async {
        bool parentState = false;

        await tester.pumpWidget(
          StatefulBuilder(
            builder: (context, setState) {
              return MaterialApp(
                home: Scaffold(
                  body: Column(
                    children: [
                      Text('Parent state: $parentState'),
                      StreakWidget(
                        currentStreak: 3,
                      ),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            parentState = !parentState;
                          });
                        },
                        child: Text('Toggle Parent'),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );

        expect(find.text('3 streaks'), findsOneWidget);

        // Change parent state
        await tester.tap(find.text('Toggle Parent'));
        await tester.pumpAndSettle();

        // Streak widget should maintain its display
        expect(find.text('3 streaks'), findsOneWidget);
      });
    });
  });
}
