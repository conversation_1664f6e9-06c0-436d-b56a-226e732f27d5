import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:unstack/widgets/task_card.dart';
import 'package:unstack/models/tasks/task.model.dart';
import '../helpers/test_helpers.dart';
import '../helpers/test_constants.dart';

void main() {
  group('TaskCard Widget Tests', () {
    late Task testTask;

    setUp(() {
      testTask = TestHelpers.createTestTask(
        id: 'test_task_1',
        title: 'Test Task Title',
        description: 'Test Task Description',
        priority: TaskPriority.medium,
        isCompleted: false,
      );
    });

    group('Task Card Rendering', () {
      testWidgets('should render task card with basic information', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: TaskCard(
                task: testTask,
                key: Key(TestConstants.taskCardKey),
              ),
            ),
          ),
        );

        expect(find.text('Test Task Title'), findsOneWidget);
        expect(find.text('Test Task Description'), findsOneWidget);
        expect(find.byKey(Key(TestConstants.taskCardKey)), findsOneWidget);
      });

      testWidgets('should render completed task card differently', (WidgetTester tester) async {
        final completedTask = testTask.copyWith(
          isCompleted: true,
          completedAt: DateTime.now(),
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: TaskCard(
                task: completedTask,
              ),
            ),
          ),
        );

        expect(find.text('Test Task Title'), findsOneWidget);
        expect(find.text('Test Task Description'), findsOneWidget);
        
        // Should show completed state
        // Note: Actual implementation may have different visual indicators
      });

      testWidgets('should render task with different priorities', (WidgetTester tester) async {
        for (final priority in TaskPriority.values) {
          final priorityTask = testTask.copyWith(priority: priority);

          await tester.pumpWidget(
            MaterialApp(
              home: Scaffold(
                body: TaskCard(
                  task: priorityTask,
                ),
              ),
            ),
          );

          expect(find.text('Test Task Title'), findsOneWidget);
          
          // Each priority should render without errors
          await tester.pumpAndSettle();
        }
      });

      testWidgets('should handle long task titles and descriptions', (WidgetTester tester) async {
        final longTask = testTask.copyWith(
          title: TestConstants.longTaskTitle,
          description: TestConstants.longTaskDescription,
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: TaskCard(
                task: longTask,
              ),
            ),
          ),
        );

        // Should render without overflow errors
        expect(find.textContaining('This is a very long task title'), findsOneWidget);
        expect(find.textContaining('This is a very long task description'), findsOneWidget);
        
        await tester.pumpAndSettle();
      });
    });

    group('Task Card Interactions', () {
      testWidgets('should call onTap when task card is tapped', (WidgetTester tester) async {
        bool tapCalled = false;

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: TaskCard(
                task: testTask,
                onTap: () {
                  tapCalled = true;
                },
              ),
            ),
          ),
        );

        await tester.tap(find.byType(TaskCard));
        await tester.pumpAndSettle();

        expect(tapCalled, true);
      });

      testWidgets('should call onToggleComplete when checkbox is tapped', (WidgetTester tester) async {
        bool toggleCalled = false;
        bool toggleValue = false;

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: TaskCard(
                task: testTask,
                onToggleComplete: (value) {
                  toggleCalled = true;
                  toggleValue = value;
                },
              ),
            ),
          ),
        );

        // Find and tap the checkbox (implementation may vary)
        final checkboxFinder = find.byType(Checkbox);
        if (checkboxFinder.evaluate().isNotEmpty) {
          await tester.tap(checkboxFinder);
          await tester.pumpAndSettle();

          expect(toggleCalled, true);
        }
      });

      testWidgets('should call onDelete when delete action is triggered', (WidgetTester tester) async {
        bool deleteCalled = false;

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: TaskCard(
                task: testTask,
                onDelete: () {
                  deleteCalled = true;
                },
              ),
            ),
          ),
        );

        // Look for delete button or swipe action
        // Implementation depends on actual TaskCard design
        final deleteFinder = find.byIcon(Icons.delete);
        if (deleteFinder.evaluate().isNotEmpty) {
          await tester.tap(deleteFinder);
          await tester.pumpAndSettle();

          expect(deleteCalled, true);
        }
      });

      testWidgets('should show visual feedback on press', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: TaskCard(
                task: testTask,
              ),
            ),
          ),
        );

        // Test press down
        await tester.press(find.byType(TaskCard));
        await tester.pump(Duration(milliseconds: 100));

        // Should show pressed state (scale animation)
        // Exact verification depends on implementation

        // Release press
        await tester.pumpAndSettle();
      });
    });

    group('Task Card Animations', () {
      testWidgets('should animate completion state change', (WidgetTester tester) async {
        bool isCompleted = false;

        await tester.pumpWidget(
          StatefulBuilder(
            builder: (context, setState) {
              return MaterialApp(
                home: Scaffold(
                  body: Column(
                    children: [
                      TaskCard(
                        task: testTask.copyWith(isCompleted: isCompleted),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            isCompleted = !isCompleted;
                          });
                        },
                        child: Text('Toggle'),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );

        // Tap toggle button
        await tester.tap(find.text('Toggle'));
        await tester.pump();

        // Should start animation
        await tester.pump(Duration(milliseconds: 150));
        
        // Complete animation
        await tester.pumpAndSettle();
      });

      testWidgets('should handle rapid state changes', (WidgetTester tester) async {
        bool isCompleted = false;

        await tester.pumpWidget(
          StatefulBuilder(
            builder: (context, setState) {
              return MaterialApp(
                home: Scaffold(
                  body: Column(
                    children: [
                      TaskCard(
                        task: testTask.copyWith(isCompleted: isCompleted),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            isCompleted = !isCompleted;
                          });
                        },
                        child: Text('Toggle'),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );

        // Rapid toggles
        for (int i = 0; i < 5; i++) {
          await tester.tap(find.text('Toggle'));
          await tester.pump(Duration(milliseconds: 50));
        }

        await tester.pumpAndSettle();
        
        // Should handle rapid changes without errors
      });
    });

    group('Task Card Accessibility', () {
      testWidgets('should have proper accessibility labels', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: TaskCard(
                task: testTask,
              ),
            ),
          ),
        );

        // Check for semantic labels
        expect(find.bySemanticsLabel('Test Task Title'), findsOneWidget);
        
        // Should have proper semantics for screen readers
        final semantics = tester.getSemantics(find.byType(TaskCard));
        expect(semantics.hasAction(SemanticsAction.tap), true);
      });

      testWidgets('should support keyboard navigation', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: TaskCard(
                task: testTask,
                onTap: () {},
              ),
            ),
          ),
        );

        // Focus the task card
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pumpAndSettle();

        // Should be focusable
        expect(find.byType(TaskCard), findsOneWidget);
      });
    });

    group('Task Card Edge Cases', () {
      testWidgets('should handle null callbacks gracefully', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: TaskCard(
                task: testTask,
                // All callbacks are null
              ),
            ),
          ),
        );

        // Should render without errors
        expect(find.byType(TaskCard), findsOneWidget);
        
        // Tapping should not cause errors
        await tester.tap(find.byType(TaskCard));
        await tester.pumpAndSettle();
      });

      testWidgets('should handle empty task data', (WidgetTester tester) async {
        final emptyTask = testTask.copyWith(
          title: '',
          description: '',
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: TaskCard(
                task: emptyTask,
              ),
            ),
          ),
        );

        // Should render without errors even with empty data
        expect(find.byType(TaskCard), findsOneWidget);
      });

      testWidgets('should handle compact mode', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: TaskCard(
                task: testTask,
                isCompact: true,
              ),
            ),
          ),
        );

        expect(find.byType(TaskCard), findsOneWidget);
        
        // Compact mode should render differently
        // Exact verification depends on implementation
      });
    });
  });
}
