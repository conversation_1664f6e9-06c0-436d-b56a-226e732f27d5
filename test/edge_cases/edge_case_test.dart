import 'package:flutter_test/flutter_test.dart';
import 'package:unstack/providers/task_provider.dart';
import 'package:unstack/providers/streak_provider.dart';
import 'package:unstack/models/tasks/task.model.dart';
import '../helpers/test_helpers.dart';
import '../helpers/test_constants.dart';

void main() {
  group('Edge Case Tests', () {
    late TaskProvider taskProvider;
    late StreakProvider streakProvider;

    setUp(() {
      TestHelpers.resetTestData();
      taskProvider = TaskProvider();
      streakProvider = StreakProvider();
    });

    tearDown(() {
      TestHelpers.resetTestData();
    });

    group('Date Boundary Conditions', () {
      test('should handle midnight transitions correctly', () async {
        final now = DateTime.now();
        final midnight = DateTime(now.year, now.month, now.day);
        final justBeforeMidnight = midnight.subtract(Duration(microseconds: 1));
        final justAfterMidnight = midnight.add(Duration(microseconds: 1));

        final taskBefore = TestHelpers.createTestTask(
          id: 'before_midnight',
          createdAt: justBeforeMidnight,
        );
        final taskAfter = TestHelpers.createTestTask(
          id: 'after_midnight',
          createdAt: justAfterMidnight,
        );

        await taskProvider.addTask(taskBefore);
        await taskProvider.addTask(taskAfter);

        // Tasks should be assigned to correct days
        final beforeDay = TestHelpers.normalizeDate(justBeforeMidnight);
        final afterDay = TestHelpers.normalizeDate(justAfterMidnight);

        final tasksBeforeDay = taskProvider.getTasksForDate(beforeDay);
        final tasksAfterDay = taskProvider.getTasksForDate(afterDay);

        if (TestHelpers.isSameDay(beforeDay, afterDay)) {
          // Same day
          expect(tasksBeforeDay.length + tasksAfterDay.length, 2);
        } else {
          // Different days
          expect(tasksBeforeDay.length, 1);
          expect(tasksAfterDay.length, 1);
        }
      });

      test('should handle leap year dates', () async {
        final leapYearDate = DateTime(2024, 2, 29); // Leap year
        final task = TestHelpers.createTestTask(
          id: 'leap_year_task',
          createdAt: leapYearDate,
        );

        await taskProvider.addTask(task);
        await taskProvider.markTaskAsCompleted(task);

        final tasksForDate = taskProvider.getTasksForDate(leapYearDate);
        expect(tasksForDate.length, 0); // Moved to completed

        final completedForDate = taskProvider.getCompletedTasksForDate(leapYearDate);
        expect(completedForDate.length, 1);

        await streakProvider.updateStreakFromTaskProvider(taskProvider, leapYearDate);
        expect(streakProvider.currentStreak, greaterThan(0));
      });

      test('should handle year boundaries', () async {
        final lastDayOfYear = DateTime(2023, 12, 31, 23, 59, 59);
        final firstDayOfYear = DateTime(2024, 1, 1, 0, 0, 1);

        final taskLastYear = TestHelpers.createTestTask(
          id: 'last_year_task',
          createdAt: lastDayOfYear,
        );
        final taskNewYear = TestHelpers.createTestTask(
          id: 'new_year_task',
          createdAt: firstDayOfYear,
        );

        await taskProvider.addTask(taskLastYear);
        await taskProvider.addTask(taskNewYear);

        await taskProvider.markTaskAsCompleted(taskLastYear);
        await taskProvider.markTaskAsCompleted(taskNewYear);

        // Should handle year boundary in streak calculation
        await streakProvider.updateStreakFromTaskProvider(taskProvider, lastDayOfYear);
        await streakProvider.updateStreakFromTaskProvider(taskProvider, firstDayOfYear);

        expect(streakProvider.currentStreak, greaterThan(0));
      });
    });

    group('Extreme Data Scenarios', () {
      test('should handle very old dates', () async {
        final veryOldDate = DateTime(1900, 1, 1);
        final task = TestHelpers.createTestTask(
          id: 'very_old_task',
          createdAt: veryOldDate,
        );

        await taskProvider.addTask(task);
        await taskProvider.markTaskAsCompleted(task);

        final tasksForDate = taskProvider.getCompletedTasksForDate(veryOldDate);
        expect(tasksForDate.length, 1);

        await streakProvider.updateStreakFromTaskProvider(taskProvider, veryOldDate);
        // Should not crash
      });

      test('should handle future dates', () async {
        final futureDate = DateTime.now().add(Duration(days: 365));
        final task = TestHelpers.createTestTask(
          id: 'future_task',
          createdAt: futureDate,
        );

        await taskProvider.addTask(task);
        await taskProvider.markTaskAsCompleted(task);

        final tasksForDate = taskProvider.getCompletedTasksForDate(futureDate);
        expect(tasksForDate.length, 1);

        await streakProvider.updateStreakFromTaskProvider(taskProvider, futureDate);
        // Should handle future dates gracefully
      });

      test('should handle maximum streak length', () async {
        final today = DateTime.now();
        
        // Create maximum possible streak (1 year)
        for (int i = 0; i < TestConstants.maxStreakLength; i++) {
          final date = today.subtract(Duration(days: i));
          await streakProvider.updateStreakForDate(date, 1, 1, true);
        }

        await streakProvider.loadStreakData();
        
        // Should handle very long streaks
        expect(streakProvider.currentStreak, greaterThan(0));
        expect(streakProvider.longestStreak, greaterThan(0));
      });

      test('should handle zero and negative task counts', () async {
        final today = DateTime.now();

        // Zero tasks
        await streakProvider.updateStreakForDate(today, 0, 0, false);
        expect(streakProvider.currentStreak, 0);

        // Negative tasks (shouldn't happen but test robustness)
        await streakProvider.updateStreakForDate(today, -1, -1, false);
        // Should not crash
      });
    });

    group('Rapid Operations', () {
      test('should handle rapid task additions', () async {
        final today = DateTime.now();
        final tasks = List.generate(
          100,
          (i) => TestHelpers.createTestTask(
            id: 'rapid_task_$i',
            createdAt: today,
          ),
        );

        final stopwatch = Stopwatch()..start();

        // Rapid additions
        for (final task in tasks) {
          await taskProvider.addTask(task);
        }

        stopwatch.stop();
        
        expect(taskProvider.tasks.length, 100);
        expect(stopwatch.elapsed, lessThan(TestConstants.longTimeout));
      });

      test('should handle rapid task completions', () async {
        final today = DateTime.now();
        final tasks = List.generate(
          50,
          (i) => TestHelpers.createTestTask(
            id: 'completion_task_$i',
            createdAt: today,
          ),
        );

        // Add all tasks first
        for (final task in tasks) {
          await taskProvider.addTask(task);
        }

        final stopwatch = Stopwatch()..start();

        // Rapid completions
        for (final task in tasks) {
          await taskProvider.markTaskAsCompleted(task);
        }

        stopwatch.stop();

        expect(taskProvider.completedTasks.length, 50);
        expect(taskProvider.tasks.length, 0);
        expect(stopwatch.elapsed, lessThan(TestConstants.longTimeout));
      });

      test('should handle rapid streak updates', () async {
        final today = DateTime.now();

        final stopwatch = Stopwatch()..start();

        // Rapid streak updates
        for (int i = 0; i < 100; i++) {
          final date = today.subtract(Duration(days: i));
          await streakProvider.updateStreakForDate(date, 1, 1, true);
        }

        stopwatch.stop();

        expect(stopwatch.elapsed, lessThan(TestConstants.longTimeout));
        await streakProvider.loadStreakData();
        expect(streakProvider.currentStreak, greaterThan(0));
      });

      test('should handle concurrent operations', () async {
        final today = DateTime.now();
        final tasks = List.generate(
          20,
          (i) => TestHelpers.createTestTask(
            id: 'concurrent_task_$i',
            createdAt: today,
          ),
        );

        // Concurrent operations
        final futures = <Future>[];
        
        for (final task in tasks) {
          futures.add(taskProvider.addTask(task));
        }
        
        for (final task in tasks) {
          futures.add(taskProvider.markTaskAsCompleted(task));
        }

        await Future.wait(futures);

        // Should handle concurrent operations without data corruption
        expect(taskProvider.completedTasks.length, 20);
      });
    });

    group('Memory and Resource Management', () {
      test('should not leak memory with large datasets', () async {
        final today = DateTime.now();

        // Create and destroy large datasets repeatedly
        for (int cycle = 0; cycle < 10; cycle++) {
          final tasks = List.generate(
            TestConstants.mediumDataSet,
            (i) => TestHelpers.createTestTask(
              id: 'memory_test_${cycle}_$i',
              createdAt: today,
            ),
          );

          // Add all tasks
          for (final task in tasks) {
            await taskProvider.addTask(task);
          }

          // Delete all tasks
          await taskProvider.deleteAllTasks();
          
          expect(taskProvider.tasks.length, 0);
        }

        // Should complete without memory issues
      });

      test('should handle resource cleanup on provider disposal', () async {
        final tempTaskProvider = TaskProvider();
        final tempStreakProvider = StreakProvider();

        final task = TestHelpers.createTestTask();
        await tempTaskProvider.addTask(task);
        await tempStreakProvider.updateStreakForToday(1, 1, true);

        // Dispose providers (in real app, this happens automatically)
        tempTaskProvider.dispose();
        tempStreakProvider.dispose();

        // Should not crash
      });
    });

    group('Data Integrity Edge Cases', () {
      test('should handle corrupted task data', () async {
        // Simulate corrupted task with invalid data
        final corruptedTask = Task(
          id: '', // Empty ID
          title: '', // Empty title
          description: '', // Empty description
          priority: TaskPriority.medium,
          createdAt: DateTime.now(),
          priorityIndex: -1, // Invalid priority index
          isCompleted: false,
        );

        await taskProvider.addTask(corruptedTask);
        
        // Should handle gracefully
        expect(taskProvider.tasks.length, 1);
      });

      test('should handle inconsistent completion states', () async {
        final task = TestHelpers.createTestTask(
          isCompleted: true,
          completedAt: null, // Inconsistent: completed but no completion date
        );

        await taskProvider.addTask(task);
        
        // Should handle inconsistent state
        expect(taskProvider.tasks.length, 1);
      });

      test('should handle duplicate task IDs', () async {
        final task1 = TestHelpers.createTestTask(id: 'duplicate_id');
        final task2 = TestHelpers.createTestTask(id: 'duplicate_id');

        await taskProvider.addTask(task1);
        await taskProvider.addTask(task2);

        // Should handle duplicates (behavior depends on implementation)
        expect(taskProvider.tasks.length, greaterThanOrEqualTo(1));
      });
    });

    group('Timezone and Locale Edge Cases', () {
      test('should handle different timezone dates consistently', () async {
        // Create dates in different timezones
        final utcDate = DateTime.utc(2024, 1, 15, 12, 0, 0);
        final localDate = DateTime(2024, 1, 15, 12, 0, 0);

        final utcTask = TestHelpers.createTestTask(
          id: 'utc_task',
          createdAt: utcDate,
        );
        final localTask = TestHelpers.createTestTask(
          id: 'local_task',
          createdAt: localDate,
        );

        await taskProvider.addTask(utcTask);
        await taskProvider.addTask(localTask);

        // Should handle timezone differences consistently
        final utcTasks = taskProvider.getTasksForDate(utcDate);
        final localTasks = taskProvider.getTasksForDate(localDate);

        expect(utcTasks.length, greaterThanOrEqualTo(1));
        expect(localTasks.length, greaterThanOrEqualTo(1));
      });

      test('should handle daylight saving time transitions', () async {
        // Simulate DST transition dates (these are approximate)
        final beforeDST = DateTime(2024, 3, 9, 23, 0, 0); // Before spring forward
        final afterDST = DateTime(2024, 3, 10, 1, 0, 0);  // After spring forward

        final taskBefore = TestHelpers.createTestTask(
          id: 'before_dst',
          createdAt: beforeDST,
        );
        final taskAfter = TestHelpers.createTestTask(
          id: 'after_dst',
          createdAt: afterDST,
        );

        await taskProvider.addTask(taskBefore);
        await taskProvider.addTask(taskAfter);

        // Should handle DST transitions correctly
        expect(taskProvider.tasks.length, 2);
      });
    });

    group('Error Recovery', () {
      test('should recover from database errors', () async {
        // Simulate database error scenario
        final task = TestHelpers.createTestTask();
        
        try {
          await taskProvider.addTask(task);
          // Force an error state
          await taskProvider.deleteTask('non_existent_task');
        } catch (e) {
          // Should handle errors gracefully
        }

        // Should be able to continue operations
        final newTask = TestHelpers.createTestTask(id: 'recovery_task');
        await taskProvider.addTask(newTask);
        
        expect(taskProvider.tasks.isNotEmpty, true);
      });

      test('should handle provider state corruption', () async {
        // Simulate state corruption
        final task = TestHelpers.createTestTask();
        await taskProvider.addTask(task);

        // Clear error state
        taskProvider.clearError();
        
        // Should be able to continue
        expect(taskProvider.hasError, false);
      });
    });
  });
}
