import 'package:flutter_test/flutter_test.dart';
import 'package:unstack/models/tasks/task.model.dart';
import 'package:unstack/providers/task_provider.dart';
import '../helpers/test_helpers.dart';
import '../helpers/test_constants.dart';

void main() {
  group('TaskProvider Unit Tests', () {
    late TaskProvider taskProvider;

    setUp(() {
      TestHelpers.resetTestData();
      taskProvider = TaskProvider();
    });

    tearDown(() {
      TestHelpers.resetTestData();
    });

    group('Task CRUD Operations', () {
      test('should add task successfully', () async {
        final task = TestHelpers.createTestTask(
          id: 'test_task_1',
          title: 'Test Task 1',
        );

        final result = await taskProvider.addTask(task);

        expect(result, true);
        expect(taskProvider.tasks.length, 1);
        expect(taskProvider.tasks.first.id, 'test_task_1');
        expect(taskProvider.tasks.first.title, 'Test Task 1');
      });

      test('should handle add task failure gracefully', () async {
        // Create invalid task (null id should cause failure in real implementation)
        final task = TestHelpers.createTestTask(title: '');

        final result = await taskProvider.addTask(task);

        // In mock implementation, this should still succeed
        // In real implementation with validation, this might fail
        expect(result, true);
      });

      test('should update task successfully', () async {
        final originalTask = TestHelpers.createTestTask(
          id: 'test_task_1',
          title: 'Original Title',
        );

        await taskProvider.addTask(originalTask);

        final updatedTask = originalTask.copyWith(
          title: 'Updated Title',
          description: 'Updated Description',
        );

        final result = await taskProvider.updateTask(updatedTask);

        expect(result, true);
        final retrievedTask = taskProvider.getTaskById('test_task_1');
        expect(retrievedTask?.title, 'Updated Title');
        expect(retrievedTask?.description, 'Updated Description');
      });

      test('should delete task successfully', () async {
        final task = TestHelpers.createTestTask(id: 'test_task_1');
        await taskProvider.addTask(task);

        expect(taskProvider.tasks.length, 1);

        final result = await taskProvider.deleteTask('test_task_1');

        expect(result, true);
        expect(taskProvider.tasks.length, 0);
        expect(taskProvider.getTaskById('test_task_1'), null);
      });

      test('should delete all tasks successfully', () async {
        final tasks =
            TestHelpers.createTestTasksForDate(DateTime.now(), count: 5);
        for (final task in tasks) {
          await taskProvider.addTask(task);
        }

        expect(taskProvider.tasks.length, 5);

        final result = await taskProvider.deleteAllTasks();

        expect(result, true);
        expect(taskProvider.tasks.length, 0);
      });
    });

    group('Task Completion Operations', () {
      test('should mark task as completed', () async {
        final task = TestHelpers.createTestTask(
          id: 'test_task_1',
          isCompleted: false,
        );

        await taskProvider.addTask(task);
        expect(taskProvider.tasks.length, 1);
        expect(taskProvider.completedTasks.length, 0);

        final result = await taskProvider.markTaskAsCompleted(task);

        expect(result, true);
        expect(taskProvider.tasks.length, 0);
        expect(taskProvider.completedTasks.length, 1);
        expect(taskProvider.completedTasks.first.isCompleted, true);
        expect(taskProvider.completedTasks.first.completedAt, isNotNull);
      });

      test('should mark task as incomplete', () async {
        final completedTask = TestHelpers.createTestTask(
          id: 'test_task_1',
          isCompleted: true,
          completedAt: DateTime.now(),
        );

        await taskProvider.addTask(completedTask);
        await taskProvider.markTaskAsCompleted(completedTask);

        expect(taskProvider.completedTasks.length, 1);
        expect(taskProvider.tasks.length, 0);

        final result = await taskProvider.markTaskAsIncomplete(completedTask);

        expect(result, true);
        expect(taskProvider.tasks.length, 1);
        expect(taskProvider.completedTasks.length, 0);
        expect(taskProvider.tasks.first.isCompleted, false);
        expect(taskProvider.tasks.first.completedAt, null);
      });

      test('should toggle task completion status', () async {
        final task = TestHelpers.createTestTask(
          id: 'test_task_1',
          isCompleted: false,
        );

        await taskProvider.addTask(task);

        // Toggle to completed
        final result1 = await taskProvider.toggleTaskCompletion(task);
        expect(result1, true);
        expect(taskProvider.completedTasks.length, 1);
        expect(taskProvider.tasks.length, 0);

        // Toggle back to incomplete
        final completedTask = taskProvider.completedTasks.first;
        final result2 = await taskProvider.toggleTaskCompletion(completedTask);
        expect(result2, true);
        expect(taskProvider.tasks.length, 1);
        expect(taskProvider.completedTasks.length, 0);
      });
    });

    group('Task Filtering and Queries', () {
      test('should filter tasks by date correctly', () async {
        final today = DateTime.now();
        final yesterday = today.subtract(Duration(days: 1));
        final tomorrow = today.add(Duration(days: 1));

        final todayTasks = TestHelpers.createTestTasksForDate(today, count: 2);
        final yesterdayTasks =
            TestHelpers.createTestTasksForDate(yesterday, count: 3);
        final tomorrowTasks =
            TestHelpers.createTestTasksForDate(tomorrow, count: 1);

        for (final task in [
          ...todayTasks,
          ...yesterdayTasks,
          ...tomorrowTasks
        ]) {
          await taskProvider.addTask(task);
        }

        final todayFiltered = taskProvider.getTasksForDate(today);
        final yesterdayFiltered = taskProvider.getTasksForDate(yesterday);
        final tomorrowFiltered = taskProvider.getTasksForDate(tomorrow);

        expect(todayFiltered.length, 2);
        expect(yesterdayFiltered.length, 3);
        expect(tomorrowFiltered.length, 1);

        // Verify all tasks in each filter belong to the correct date
        for (final task in todayFiltered) {
          expect(TestHelpers.isSameDay(task.createdAt, today), true);
        }
        for (final task in yesterdayFiltered) {
          expect(TestHelpers.isSameDay(task.createdAt, yesterday), true);
        }
        for (final task in tomorrowFiltered) {
          expect(TestHelpers.isSameDay(task.createdAt, tomorrow), true);
        }
      });

      test('should get completed tasks for date correctly', () async {
        final today = DateTime.now();
        final tasks = TestHelpers.createTestTasksForDate(
          today,
          count: 4,
          completionStates: [true, false, true, false],
        );

        for (final task in tasks) {
          await taskProvider.addTask(task);
          if (task.isCompleted) {
            await taskProvider.markTaskAsCompleted(task);
          }
        }

        final completedForToday = taskProvider.getCompletedTasksForDate(today);
        expect(completedForToday.length, 2);

        for (final task in completedForToday) {
          expect(task.isCompleted, true);
          expect(TestHelpers.isSameDay(task.createdAt, today), true);
        }
      });

      test('should check if all tasks for date are completed', () async {
        final today = DateTime.now();
        final yesterday = today.subtract(Duration(days: 1));

        // Today: all completed
        final todayTasks = TestHelpers.createTestTasksForDate(today,
            count: 3, allCompleted: true);
        // Yesterday: partially completed
        final yesterdayTasks = TestHelpers.createTestTasksForDate(
          yesterday,
          count: 3,
          completionStates: [true, false, true],
        );

        for (final task in [...todayTasks, ...yesterdayTasks]) {
          await taskProvider.addTask(task);
          if (task.isCompleted) {
            await taskProvider.markTaskAsCompleted(task);
          }
        }

        expect(taskProvider.areAllTasksCompletedForDate(today), true);
        expect(taskProvider.areAllTasksCompletedForDate(yesterday), false);
      });

      test('should return false for all completed when no tasks exist for date',
          () async {
        final today = DateTime.now();
        expect(taskProvider.areAllTasksCompletedForDate(today), false);
      });
    });

    group('Task Statistics', () {
      test('should calculate task statistics correctly', () async {
        final today = DateTime.now();
        final tasks = TestHelpers.createTestTasksForDate(
          today,
          count: 5,
          completionStates: [true, true, false, false, false],
        );

        for (final task in tasks) {
          await taskProvider.addTask(task);
          if (task.isCompleted) {
            await taskProvider.markTaskAsCompleted(task);
          }
        }

        expect(taskProvider.totalTasks, 5);
        expect(taskProvider.completedTasksCount, 2);
        expect(taskProvider.todaysTasksCompleted, false);
      });

      test('should identify when today\'s tasks are completed', () async {
        final today = DateTime.now();
        final tasks = TestHelpers.createTestTasksForDate(today,
            count: 3, allCompleted: true);

        for (final task in tasks) {
          await taskProvider.addTask(task);
          await taskProvider.markTaskAsCompleted(task);
        }

        expect(taskProvider.todaysTasksCompleted, true);
      });
    });

    group('Error Handling', () {
      test('should handle task not found gracefully', () async {
        final task = taskProvider.getTaskById('non_existent_task');
        expect(task, null);
      });

      test('should clear error state', () async {
        // Simulate error state
        taskProvider.clearError();
        expect(taskProvider.hasError, false);
        expect(taskProvider.errorMessage, null);
      });
    });

    group('Edge Cases', () {
      test('should handle empty task list operations', () async {
        expect(taskProvider.tasks.isEmpty, true);
        expect(taskProvider.completedTasks.isEmpty, true);
        expect(taskProvider.totalTasks, 0);
        expect(taskProvider.completedTasksCount, 0);
      });

      test('should handle rapid task operations', () async {
        final tasks =
            List.generate(10, (i) => TestHelpers.createTestTask(id: 'task_$i'));

        // Add all tasks rapidly
        final futures = tasks.map((task) => taskProvider.addTask(task));
        final results = await Future.wait(futures);

        expect(results.every((result) => result == true), true);
        expect(taskProvider.tasks.length, 10);
      });

      test('should handle task with same ID', () async {
        final task1 = TestHelpers.createTestTask(id: 'duplicate_id');
        final task2 = TestHelpers.createTestTask(
            id: 'duplicate_id', title: 'Different Title');

        await taskProvider.addTask(task1);
        await taskProvider.addTask(task2);

        // Should have both tasks (mock doesn't enforce unique IDs)
        expect(taskProvider.tasks.length, 2);
      });
    });
  });
}
