import 'package:flutter_test/flutter_test.dart';
import 'package:unstack/logic/streak/streak_impl.dart';
import 'package:unstack/models/streak/streak.model.dart';
import '../helpers/test_helpers.dart';
import '../helpers/test_constants.dart';

void main() {
  group('StreakManager Unit Tests', () {
    late StreakManager streakManager;

    setUp(() {
      TestHelpers.resetTestData();
      streakManager = StreakManager();
    });

    tearDown(() {
      TestHelpers.resetTestData();
    });

    group('Day Completion Updates', () {
      test('should update day completion for today', () async {
        const todayTasks = 3;
        const completedTasks = 3;
        const allTasksCompleted = true;

        await streakManager.updateDayCompletion(
          todayTasks,
          completedTasks,
          allTasksCompleted,
        );

        final currentStreak = await streakManager.getCurrentStreak();
        expect(currentStreak, greaterThan(0));
      });

      test('should update day completion for specific date', () async {
        final specificDate = DateTime.now().subtract(Duration(days: 2));
        const tasks = 2;
        const completed = 2;
        const allCompleted = true;

        await streakManager.updateDayCompletionForDate(
          specificDate,
          tasks,
          completed,
          allCompleted,
        );

        final history = await streakManager.getCompletionHistory();
        expect(history.isNotEmpty, true);
        
        final dateEntry = history.firstWhere(
          (entry) => TestHelpers.isSameDay(entry.date, specificDate),
          orElse: () => StreakModel(
            date: specificDate,
            totalTasks: 0,
            completedTasks: 0,
            allTasksCompleted: false,
          ),
        );
        
        expect(dateEntry.totalTasks, tasks);
        expect(dateEntry.completedTasks, completed);
        expect(dateEntry.allTasksCompleted, allCompleted);
      });

      test('should not earn streak when tasks incomplete', () async {
        const todayTasks = 4;
        const completedTasks = 3;
        const allTasksCompleted = false;

        await streakManager.updateDayCompletion(
          todayTasks,
          completedTasks,
          allTasksCompleted,
        );

        final currentStreak = await streakManager.getCurrentStreak();
        expect(currentStreak, 0);
      });
    });

    group('Streak Removal', () {
      test('should remove streak for specific date', () async {
        final targetDate = DateTime.now().subtract(Duration(days: 1));
        
        // First, add streak data for the date
        await streakManager.updateDayCompletionForDate(
          targetDate,
          3, 3, true,
        );

        // Verify it exists
        final historyBefore = await streakManager.getCompletionHistory();
        expect(historyBefore.any((entry) => TestHelpers.isSameDay(entry.date, targetDate)), true);

        // Remove streak for the date
        await streakManager.removeStreakForDate(targetDate);

        // Verify it's removed
        final historyAfter = await streakManager.getCompletionHistory();
        expect(historyAfter.any((entry) => TestHelpers.isSameDay(entry.date, targetDate)), false);
      });
    });

    group('Completion History', () {
      test('should retrieve completion history', () async {
        final dates = [
          DateTime.now().subtract(Duration(days: 3)),
          DateTime.now().subtract(Duration(days: 2)),
          DateTime.now().subtract(Duration(days: 1)),
        ];

        for (final date in dates) {
          await streakManager.updateDayCompletionForDate(
            date,
            3, 3, true,
          );
        }

        final history = await streakManager.getCompletionHistory();
        expect(history.length, greaterThanOrEqualTo(3));
      });

      test('should return empty history when no data exists', () async {
        final history = await streakManager.getCompletionHistory();
        expect(history, isEmpty);
      });
    });

    group('Current Streak Calculation', () {
      test('should calculate current streak correctly for consecutive days', () async {
        final today = DateTime.now();
        final consecutiveDates = List.generate(
          5,
          (i) => today.subtract(Duration(days: i)),
        );

        for (final date in consecutiveDates) {
          await streakManager.updateDayCompletionForDate(
            date,
            3, 3, true,
          );
        }

        final currentStreak = await streakManager.getCurrentStreak();
        expect(currentStreak, greaterThanOrEqualTo(1));
      });

      test('should break streak on incomplete day', () async {
        final today = DateTime.now();
        
        // Day 1: completed
        await streakManager.updateDayCompletionForDate(
          today.subtract(Duration(days: 2)),
          3, 3, true,
        );
        
        // Day 2: incomplete (breaks streak)
        await streakManager.updateDayCompletionForDate(
          today.subtract(Duration(days: 1)),
          3, 2, false,
        );
        
        // Day 3 (today): completed
        await streakManager.updateDayCompletionForDate(
          today,
          3, 3, true,
        );

        final currentStreak = await streakManager.getCurrentStreak();
        // Should only count from today since yesterday broke the streak
        expect(currentStreak, lessThanOrEqualTo(1));
      });

      test('should skip days with no tasks in streak calculation', () async {
        final today = DateTime.now();
        
        // Day 1: completed
        await streakManager.updateDayCompletionForDate(
          today.subtract(Duration(days: 2)),
          3, 3, true,
        );
        
        // Day 2: no tasks (should be skipped)
        // Don't add any data for this day
        
        // Day 3 (today): completed
        await streakManager.updateDayCompletionForDate(
          today,
          3, 3, true,
        );

        final currentStreak = await streakManager.getCurrentStreak();
        // Should count both days despite the gap
        expect(currentStreak, greaterThanOrEqualTo(1));
      });

      test('should return 0 for current streak when no completed days', () async {
        final currentStreak = await streakManager.getCurrentStreak();
        expect(currentStreak, 0);
      });
    });

    group('Longest Streak Calculation', () {
      test('should track longest streak correctly', () async {
        final today = DateTime.now();
        
        // Create a streak of 7 days
        for (int i = 0; i < 7; i++) {
          await streakManager.updateDayCompletionForDate(
            today.subtract(Duration(days: i)),
            3, 3, true,
          );
        }

        final longestStreak = await streakManager.getLongestStreak();
        expect(longestStreak, greaterThanOrEqualTo(1));
      });

      test('should return 0 for longest streak when no data', () async {
        final longestStreak = await streakManager.getLongestStreak();
        expect(longestStreak, 0);
      });
    });

    group('Total Completed Days', () {
      test('should count total completed days correctly', () async {
        final today = DateTime.now();
        final completedDays = [
          today.subtract(Duration(days: 5)),
          today.subtract(Duration(days: 3)),
          today.subtract(Duration(days: 1)),
        ];

        for (final date in completedDays) {
          await streakManager.updateDayCompletionForDate(
            date,
            3, 3, true,
          );
        }

        // Add incomplete days
        await streakManager.updateDayCompletionForDate(
          today.subtract(Duration(days: 4)),
          3, 2, false,
        );
        await streakManager.updateDayCompletionForDate(
          today.subtract(Duration(days: 2)),
          3, 1, false,
        );

        final totalCompleted = await streakManager.getTotalCompletedDays();
        expect(totalCompleted, greaterThanOrEqualTo(3));
      });

      test('should return 0 for total completed days when no data', () async {
        final totalCompleted = await streakManager.getTotalCompletedDays();
        expect(totalCompleted, 0);
      });
    });

    group('Streak Reset', () {
      test('should reset all streak data', () async {
        final today = DateTime.now();
        
        // Add some streak data
        for (int i = 0; i < 3; i++) {
          await streakManager.updateDayCompletionForDate(
            today.subtract(Duration(days: i)),
            3, 3, true,
          );
        }

        // Verify data exists
        final historyBefore = await streakManager.getCompletionHistory();
        expect(historyBefore.isNotEmpty, true);

        // Reset streak
        await streakManager.resetStreak();

        // Verify data is cleared
        final historyAfter = await streakManager.getCompletionHistory();
        expect(historyAfter.isEmpty, true);

        final currentStreak = await streakManager.getCurrentStreak();
        expect(currentStreak, 0);

        final longestStreak = await streakManager.getLongestStreak();
        expect(longestStreak, 0);

        final totalCompleted = await streakManager.getTotalCompletedDays();
        expect(totalCompleted, 0);
      });
    });

    group('Edge Cases and Error Handling', () {
      test('should handle invalid date inputs gracefully', () async {
        // Test with very old date
        final veryOldDate = DateTime(1900, 1, 1);
        
        await streakManager.updateDayCompletionForDate(
          veryOldDate,
          1, 1, true,
        );

        // Should not crash
        final history = await streakManager.getCompletionHistory();
        expect(history.isNotEmpty, true);
      });

      test('should handle zero and negative task counts', () async {
        final today = DateTime.now();
        
        // Zero tasks
        await streakManager.updateDayCompletion(0, 0, false);
        
        // Should not crash and should handle appropriately
        final currentStreak = await streakManager.getCurrentStreak();
        expect(currentStreak, 0);
      });

      test('should handle very large numbers', () async {
        final today = DateTime.now();
        
        await streakManager.updateDayCompletion(
          1000000, // Very large number of tasks
          1000000,
          true,
        );

        // Should handle large numbers without issues
        final currentStreak = await streakManager.getCurrentStreak();
        expect(currentStreak, greaterThan(0));
      });
    });
  });
}
