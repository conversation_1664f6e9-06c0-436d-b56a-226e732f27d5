/// Test constants and configuration values
class TestConstants {
  // Test timeouts
  static const Duration shortTimeout = Duration(seconds: 5);
  static const Duration mediumTimeout = Duration(seconds: 10);
  static const Duration longTimeout = Duration(seconds: 30);
  
  // Test delays
  static const Duration shortDelay = Duration(milliseconds: 100);
  static const Duration mediumDelay = Duration(milliseconds: 500);
  static const Duration longDelay = Duration(seconds: 1);
  
  // Test data sizes
  static const int smallDataSet = 10;
  static const int mediumDataSet = 100;
  static const int largeDataSet = 1000;
  
  // Test task data
  static const String defaultTaskTitle = 'Test Task';
  static const String defaultTaskDescription = 'Test Description';
  static const String longTaskTitle = 'This is a very long task title that should test the limits of the UI and data handling capabilities of the application';
  static const String longTaskDescription = 'This is a very long task description that contains multiple sentences and should test how the application handles large amounts of text in task descriptions. It includes various characters and should help identify any text processing issues.';
  
  // Test streak data
  static const int maxStreakLength = 365; // One year
  static const int typicalStreakLength = 7; // One week
  
  // Test date ranges
  static const int daysInWeek = 7;
  static const int daysInMonth = 30;
  static const int daysInYear = 365;
  
  // Test error messages
  static const String taskNotFoundError = 'Task not found';
  static const String databaseError = 'Database operation failed';
  static const String networkError = 'Network connection failed';
  static const String validationError = 'Validation failed';
  
  // Test widget keys
  static const String taskCardKey = 'task_card';
  static const String streakWidgetKey = 'streak_widget';
  static const String progressIndicatorKey = 'progress_indicator';
  static const String addTaskButtonKey = 'add_task_button';
  static const String completeTaskButtonKey = 'complete_task_button';
  
  // Test performance thresholds
  static const Duration maxRenderTime = Duration(milliseconds: 16); // 60 FPS
  static const Duration maxDatabaseOperationTime = Duration(milliseconds: 100);
  static const Duration maxProviderUpdateTime = Duration(milliseconds: 50);
  
  // Test data patterns
  static const List<bool> alternatingPattern = [true, false, true, false, true];
  static const List<bool> increasingPattern = [false, false, true, true, true];
  static const List<bool> decreasingPattern = [true, true, true, false, false];
  static const List<bool> randomPattern = [true, false, true, true, false, true, false];
  
  // Test priority distributions
  static const Map<String, int> priorityDistribution = {
    'urgent': 1,
    'high': 2,
    'medium': 4,
    'low': 3,
  };
}
