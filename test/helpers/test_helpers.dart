import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:unstack/models/tasks/task.model.dart';
import 'package:unstack/models/streak/streak.model.dart';
import 'package:unstack/providers/task_provider.dart';
import 'package:unstack/providers/streak_provider.dart';
import '../mocks/mock_database_service.dart';

/// Test helper utilities for creating test data and common test operations
class TestHelpers {
  static final MockDatabaseService mockDb = MockDatabaseService.instance;

  /// Reset all test data
  static void resetTestData() {
    mockDb.reset();
  }

  /// Create a test task with default values
  static Task createTestTask({
    String? id,
    String? title,
    String? description,
    TaskPriority? priority,
    DateTime? createdAt,
    DateTime? completedAt,
    bool? isCompleted,
    int? priorityIndex,
  }) {
    return Task(
      id: id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      title: title ?? 'Test Task',
      description: description ?? 'Test Description',
      priority: priority ?? TaskPriority.medium,
      createdAt: createdAt ?? DateTime.now(),
      completedAt: completedAt,
      isCompleted: isCompleted ?? false,
      priorityIndex: priorityIndex ?? 2,
    );
  }

  /// Create multiple test tasks for a specific date
  static List<Task> createTestTasksForDate(
    DateTime date, {
    int count = 3,
    bool allCompleted = false,
    List<bool>? completionStates,
  }) {
    final tasks = <Task>[];
    for (int i = 0; i < count; i++) {
      bool isCompleted = allCompleted;
      if (completionStates != null && i < completionStates.length) {
        isCompleted = completionStates[i];
      }
      
      tasks.add(createTestTask(
        id: 'task_${date.millisecondsSinceEpoch}_$i',
        title: 'Task ${i + 1} for ${date.toIso8601String().split('T')[0]}',
        createdAt: date,
        isCompleted: isCompleted,
        completedAt: isCompleted ? date.add(Duration(hours: i + 1)) : null,
      ));
    }
    return tasks;
  }

  /// Create test streak data
  static StreakModel createTestStreakModel({
    DateTime? date,
    int? totalTasks,
    int? completedTasks,
    bool? allTasksCompleted,
  }) {
    return StreakModel(
      date: date ?? DateTime.now(),
      totalTasks: totalTasks ?? 3,
      completedTasks: completedTasks ?? 3,
      allTasksCompleted: allTasksCompleted ?? true,
    );
  }

  /// Create consecutive streak data for testing
  static List<StreakModel> createConsecutiveStreakData(
    DateTime startDate,
    int days, {
    bool allCompleted = true,
    List<bool>? completionPattern,
  }) {
    final streaks = <StreakModel>[];
    for (int i = 0; i < days; i++) {
      final date = startDate.add(Duration(days: i));
      bool completed = allCompleted;
      if (completionPattern != null && i < completionPattern.length) {
        completed = completionPattern[i];
      }
      
      streaks.add(createTestStreakModel(
        date: date,
        totalTasks: 3,
        completedTasks: completed ? 3 : 2,
        allTasksCompleted: completed,
      ));
    }
    return streaks;
  }

  /// Normalize date to remove time component
  static DateTime normalizeDate(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  /// Check if two dates are the same day
  static bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  /// Create a widget test environment with providers
  static Widget createTestApp({
    required Widget child,
    TaskProvider? taskProvider,
    StreakProvider? streakProvider,
  }) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<TaskProvider>(
          create: (_) => taskProvider ?? TaskProvider(),
        ),
        ChangeNotifierProvider<StreakProvider>(
          create: (_) => streakProvider ?? StreakProvider(),
        ),
      ],
      child: MaterialApp(
        home: child,
      ),
    );
  }

  /// Wait for all async operations to complete
  static Future<void> pumpAndSettle(WidgetTester tester, [Duration? duration]) async {
    await tester.pumpAndSettle(duration ?? const Duration(seconds: 1));
  }

  /// Create test data for edge cases
  static Map<String, dynamic> createEdgeCaseData() {
    final now = DateTime.now();
    return {
      'today': normalizeDate(now),
      'yesterday': normalizeDate(now.subtract(Duration(days: 1))),
      'tomorrow': normalizeDate(now.add(Duration(days: 1))),
      'lastWeek': normalizeDate(now.subtract(Duration(days: 7))),
      'nextWeek': normalizeDate(now.add(Duration(days: 7))),
      'lastMonth': normalizeDate(DateTime(now.year, now.month - 1, now.day)),
      'nextMonth': normalizeDate(DateTime(now.year, now.month + 1, now.day)),
    };
  }

  /// Verify streak calculation manually for testing
  static int calculateExpectedStreak(List<StreakModel> history, DateTime targetDate) {
    if (history.isEmpty) return 0;
    
    final sortedHistory = List<StreakModel>.from(history)
      ..sort((a, b) => b.date.compareTo(a.date));
    
    int streak = 0;
    DateTime checkDate = normalizeDate(targetDate);
    
    // Check if target date has completed tasks
    final targetData = sortedHistory.firstWhere(
      (data) => isSameDay(data.date, checkDate),
      orElse: () => StreakModel(
        date: checkDate,
        totalTasks: 0,
        completedTasks: 0,
        allTasksCompleted: false,
      ),
    );
    
    if (targetData.totalTasks > 0 && targetData.allTasksCompleted) {
      streak = 1;
      checkDate = checkDate.subtract(Duration(days: 1));
      
      // Count backwards for consecutive completed days
      for (int i = 0; i < 365; i++) { // Safety limit
        final dayData = sortedHistory.firstWhere(
          (data) => isSameDay(data.date, checkDate),
          orElse: () => StreakModel(
            date: checkDate,
            totalTasks: 0,
            completedTasks: 0,
            allTasksCompleted: false,
          ),
        );
        
        if (dayData.totalTasks > 0 && dayData.allTasksCompleted) {
          streak++;
          checkDate = checkDate.subtract(Duration(days: 1));
        } else if (dayData.totalTasks > 0) {
          break; // Streak broken
        } else {
          checkDate = checkDate.subtract(Duration(days: 1)); // Skip days with no tasks
        }
      }
    }
    
    return streak;
  }
}
